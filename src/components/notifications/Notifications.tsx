'use client';
import { <PERSON>over, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Bell, Loader2, X } from 'lucide-react';
import { useMemo, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { AdminNotification, useAdminNotificationsQuery, UserRole } from '@/generated/graphql';
import NotificationItem, { PostNotificationItemSkeleton } from './NotificationItem';
import useNotificationActions from './hooks/useNotificationActions';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { ScrollArea } from '../ui/ScrollArea';
import { gql } from '@apollo/client';
import { Reference } from '@apollo/client';

const ITEMS_PER_PAGE = 10;

const Notifications = () => {
  const [page, setPage] = useState(1);
  console.log('page: ', page);

  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const { userData } = useAuthContext();

  const { data, loading, fetchMore, refetch } = useAdminNotificationsQuery({
    variables: {
      paginationArgs: {
        page: 1,
        limit: ITEMS_PER_PAGE,
      },
    },
    fetchPolicy: 'cache-and-network',
    nextFetchPolicy: 'cache-first',
  });

  const {
    handleUnflagNotification,
    handleRemoveNotification,
    handleMarkAsRead,
    handleNavigate,
    handleApproveClubRequest,
    handleDeclineClubRequest,
    isOpen,
    setIsOpen,
    isUnflagging,
    isRemoving,
    isDecliningClubRequest,
    isMarkingAsRead,
  } = useNotificationActions({
    refetch: async () => {
      // Refetch only the first page to get updates
      // The cache merge strategy will handle preserving existing items
      await refetch({
        paginationArgs: {
          page: 1,
          limit: ITEMS_PER_PAGE,
        },
      });
    },
    // Pass the current page count for cache management
    currentPageCount: page,
  });

  const notifications = useMemo(
    () => data?.adminNotifications?.items ?? ([] as AdminNotification[]),
    [data?.adminNotifications?.items]
  );

  const { newNotifications, seenNotifications } = useMemo(() => {
    const newNotifs = notifications.filter((notification) => !notification.isRead);
    const seenNotifs = notifications.filter((notification) => notification.isRead);
    return {
      newNotifications: newNotifs,
      seenNotifications: seenNotifs,
    };
  }, [notifications]);

  const total = useMemo(
    () => data?.adminNotifications?.total || 0,
    [data?.adminNotifications?.total]
  );

  const hasMore = useMemo(() => total / ITEMS_PER_PAGE > page, [total, page]);

  const loadMore = async () => {
    if (hasMore && !loading && !isFetchingMore) {
      setIsFetchingMore(true);
      try {
        const nextPage = page + 1;

        await fetchMore({
          variables: {
            paginationArgs: {
              page: nextPage,
              limit: ITEMS_PER_PAGE,
            },
          },
        });
        setPage(nextPage);
      } finally {
        setIsFetchingMore(false);
      }
    }
  };

  const loadMoreRef = useIntersectionObserver({
    hasNextPage: hasMore,
    fetchNextPage: loadMore,
    isFetchingNextPage: isFetchingMore,
    threshold: 0.4,
  });

  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open);
  }, []);

  // Prevent body scroll when popover is open
  useEffect(() => {
    if (isOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = originalStyle;
      };
    }
  }, [isOpen]);

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger>
        <div
          className={cn(
            'w-8 h-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full hover:ring-2 hover:ring-[#E8EBFD] hover:bg-[#F5F7FF]',
            isOpen && 'ring-2 ring-[#E8EBFD]'
          )}
        >
          <div className='relative'>
            <Bell className='h-5 w-5 text-primary' />
            {newNotifications.length > 0 && (
              <div className='absolute -top-1 -right-0.5 w-3 h-3 bg-destructive rounded-full border-2 border-white' />
            )}
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        sideOffset={10}
        align='end'
        className='bg-white w-[calc(100vw-2rem)] cursor-pointer relative overflow-hidden sm:w-[500px] sm:max-h-[700px] max-h-[80dvh] p-0 flex flex-col mx-4 sm:mx-0 overscroll-contain'
      >
        {/* Fixed header section */}
        <div className='p-4 pb-3 sm:p-6 sm:pb-3'>
          <div className='flex justify-between py-2 items-start'>
            <h3 className='text-base font-semibold text-gray-900'>Notifications</h3>
            <Button
              variant={'ghost'}
              size={'icon'}
              onClick={() => setIsOpen(false)}
              className='flex-shrink-0'
            >
              <X className='w-5 h-5 cursor-pointer' onClick={() => setIsOpen(false)} />
            </Button>
          </div>
          {newNotifications.length > 0 && (
            <div className='flex items-center justify-between flex-wrap gap-2'>
              <p className='text-gray-700 text-[13px] leading-5'>New</p>
              <Button
                variant={'ghost'}
                className='text-gray-600 text-xs sm:text-sm font-semibold px-2 py-1 h-auto'
                onClick={() => handleMarkAsRead({ isMarkAll: true })}
                disabled={isMarkingAsRead}
              >
                Mark all as read
              </Button>
            </div>
          )}
        </div>

        {/* Scrollable items section */}
        <ScrollArea className='flex-1 overflow-y-auto gap-3 flex flex-col p-4 pt-0 sm:p-6 sm:pt-0 overscroll-contain'>
          {loading && page === 1 ? (
            Array.from({ length: 6 }).map((_, index) => (
              <PostNotificationItemSkeleton key={`s-${index}`} />
            ))
          ) : (
            <>
              {/* New Notifications Section */}
              {newNotifications.length > 0 && (
                <div className='space-y-3'>
                  {newNotifications.map((item) => (
                    <NotificationItem
                      key={item.id}
                      item={item as AdminNotification}
                      actions={{
                        handleUnflagNotification,
                        handleRemoveNotification,
                        handleMarkAsRead,
                        handleNavigate,
                        handleApproveClubRequest,
                        handleDeclineClubRequest,
                        isUnflagging,
                        isRemoving,
                        isDecliningClubRequest,
                        isMarkingAsRead,
                        isOpen,
                        setIsOpen,
                      }}
                      isAdmin={userData?.role === UserRole.Admin}
                    />
                  ))}
                </div>
              )}

              {/* Seen Notifications Section */}
              {seenNotifications.length > 0 && (
                <div className='space-y-3'>
                  {(newNotifications.length > 0 || seenNotifications.length > 0) && (
                    <div className='flex items-center justify-between pt-4'>
                      <p className='text-gray-700 text-[13px] leading-5'>Seen</p>
                    </div>
                  )}
                  {seenNotifications.map((item) => (
                    <NotificationItem
                      key={item.id}
                      item={item as AdminNotification}
                      actions={{
                        handleUnflagNotification,
                        handleRemoveNotification,
                        handleMarkAsRead,
                        handleNavigate,
                        handleApproveClubRequest,
                        handleDeclineClubRequest,
                        isUnflagging,
                        isRemoving,
                        isDecliningClubRequest,
                        isMarkingAsRead,
                        isOpen,
                        setIsOpen,
                      }}
                      isAdmin={userData?.role === UserRole.Admin}
                    />
                  ))}
                </div>
              )}

              {/* Empty state */}
              {/* {notifications.length === 0 && (
                <div className='flex flex-col items-center justify-center py-8 text-center'>
                  <Bell className='h-12 w-12 text-gray-300 mb-4' />
                  <p className='text-gray-500 text-sm'>No notifications yet</p>
                </div>
              )} */}
            </>
          )}

          {hasMore && (
            <div ref={loadMoreRef} className='w-full h-8 flex items-center justify-center'>
              {isFetchingMore && <Loader2 className='h-4 w-4 animate-spin text-primary' />}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};

export default Notifications;
