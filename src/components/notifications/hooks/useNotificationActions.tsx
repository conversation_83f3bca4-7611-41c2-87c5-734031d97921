import {
  AdminNotificationsDocument,
  useAdminDeleteClubEventMutation,
  useAdminRemoveClubPostByIdMutation,
  useAdminUnflagReportsByEventIdMutation,
  useAdminUnflagReportsByPostIdMutation,
  useMarkAdminNotificationsAsReadMutation,
  useRejectedClubRequestCreationMutation,
  ClubRequest,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';
import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { gql } from '@apollo/client';
import { Reference } from '@apollo/client';

const useNotificationActions = ({
  refetch,
  currentPageCount: _currentPageCount = 1,
}: {
  refetch?: () => void;
  currentPageCount?: number;
}) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);

  const [unflagPostMutation, { loading: isUnflaggingPost }] =
    useAdminUnflagReportsByPostIdMutation();
  const [unflagEventMutation, { loading: isUnflaggingEvent }] =
    useAdminUnflagReportsByEventIdMutation();
  const [removePostMutation, { loading: isRemovingPost }] = useAdminRemoveClubPostByIdMutation();
  const [removeEventMutation, { loading: isRemovingEvent }] = useAdminDeleteClubEventMutation();
  const [markAdminNotificationsAsRead, { loading: isMarkingAsRead }] =
    useMarkAdminNotificationsAsReadMutation();
  const [declineClubRequestCreation, { loading: isDecliningClubRequest }] =
    useRejectedClubRequestCreationMutation();

  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleUnflagNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string) => {
      try {
        if (isPostNotification) {
          await unflagPostMutation({
            variables: {
              postId: notificationId,
            },
          });
        } else {
          await unflagEventMutation({
            variables: {
              eventId: notificationId,
            },
          });
        }
        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} unflagged successfully`,
          duration: TOAST_DURATION,
        });

        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to unflag notification');
      }
    },
    [refetch, unflagPostMutation, unflagEventMutation, handleApiError, toast]
  );

  const handleRemoveNotification = useCallback(
    async (isPostNotification: boolean, notificationId: string) => {
      try {
        if (isPostNotification) {
          await removePostMutation({
            variables: {
              postId: notificationId,
            },
            update: (cache) => {
              // Remove the notification from cache
              cache.modify({
                fields: {
                  adminNotifications(existing) {
                    if (!existing?.items) return existing;

                    const filteredItems = existing.items.filter((itemRef: Reference) => {
                      const item = cache.readFragment({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment NotificationItem on AdminNotification {
                            id
                            clubPost {
                              id
                            }
                          }
                        `,
                      }) as { id: string; clubPost?: { id: string } } | null;

                      return item?.clubPost?.id !== notificationId;
                    });

                    return {
                      ...existing,
                      items: filteredItems,
                      total: Math.max(0, (existing.total || 0) - 1),
                    };
                  },
                },
              });
            },
          });
        } else {
          await removeEventMutation({
            variables: {
              clubEventId: notificationId,
            },
            update: (cache) => {
              // Remove the notification from cache
              cache.modify({
                fields: {
                  adminNotifications(existing) {
                    if (!existing?.items) return existing;

                    const filteredItems = existing.items.filter((itemRef: Reference) => {
                      const item = cache.readFragment({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment NotificationEvent on AdminNotification {
                            id
                            clubEvent {
                              id
                            }
                          }
                        `,
                      }) as { id: string; clubEvent?: { id: string } } | null;

                      return item?.clubEvent?.id !== notificationId;
                    });

                    return {
                      ...existing,
                      items: filteredItems,
                      total: Math.max(0, (existing.total || 0) - 1),
                    };
                  },
                },
              });
            },
          });
        }

        toast({
          variant: 'success',
          title: `${isPostNotification ? 'Post' : 'Event'} removed successfully`,
          duration: TOAST_DURATION,
        });

        // Refetch to ensure consistency
        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to remove notification');
      }
    },
    [refetch, removePostMutation, removeEventMutation, handleApiError, toast]
  );

  const handleMarkAsRead = useCallback(
    async ({
      isMarkAll = false,
      notificationId,
    }: {
      isMarkAll?: boolean;
      notificationId?: string;
    }) => {
      try {
        await markAdminNotificationsAsRead({
          variables: {
            input: {
              all: isMarkAll,
              notificationIds: notificationId ? [notificationId] : undefined,
            },
          },
          optimisticResponse: {
            markAdminNotificationsAsRead: true,
          },
          update: (cache) => {
            // Optimistically update the cache
            cache.modify({
              fields: {
                adminNotifications(existing) {
                  if (!existing?.items) return existing;

                  const updatedItems = existing.items.map((itemRef: Reference) => {
                    const item = cache.readFragment({
                      id: itemRef.__ref,
                      fragment: gql`
                        fragment NotificationRead on AdminNotification {
                          id
                          isRead
                        }
                      `,
                    }) as { id: string; isRead: boolean } | null;

                    if (!item) return itemRef;

                    if (isMarkAll || item.id === notificationId) {
                      return cache.writeFragment({
                        id: itemRef.__ref,
                        fragment: gql`
                          fragment UpdateNotificationRead on AdminNotification {
                            isRead
                          }
                        `,
                        data: {
                          isRead: true,
                        },
                      });
                    }

                    return itemRef;
                  });

                  return {
                    ...existing,
                    items: updatedItems,
                  };
                },
              },
            });
          },
        });

        // Still refetch to ensure server state is in sync
        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to mark admin notifications as read');
      }
    },
    [markAdminNotificationsAsRead, refetch, handleApiError]
  );

  const handleNavigate = useCallback(
    ({
      isPostReport,
      associationId,
      clubId,
      isClubCreationRequest,
    }: {
      isPostReport: boolean;
      associationId?: string;
      clubId?: string;
      isClubCreationRequest?: boolean;
    }) => {
      if (isClubCreationRequest) {
        navigate('/clubs?tab=requests');
        return;
      }

      const navigatePath = `/associations/${associationId}/clubs/${clubId}`;
      if (isPostReport) {
        navigate(`${navigatePath}?tab=posts`);
      } else {
        navigate(`${navigatePath}?tab=events`);
      }
    },
    [navigate]
  );

  const handleApproveClubRequest = useCallback(
    (clubRequest: ClubRequest) => {
      // Extract the required fields from the club request
      const clubRequestData = {
        category: clubRequest.category,
        clubAbout: clubRequest.clubAbout,
        clubDescription: clubRequest.clubDescription,
        clubName: clubRequest.clubName,
        requestId: clubRequest.id,
      };

      // Navigate to club creation page with pre-filled data
      navigate('/clubs/create', {
        state: {
          fromClubRequest: true,
          clubRequestData,
        },
      });

      setIsOpen(false);
    },
    [navigate]
  );

  const handleDeclineClubRequest = useCallback(
    async (requestId: string) => {
      try {
        await declineClubRequestCreation({
          variables: {
            clubRequestId: requestId,
          },
          refetchQueries: [AdminNotificationsDocument],
        });

        toast({
          variant: 'success',
          title: 'Club request declined successfully',
          duration: TOAST_DURATION,
        });

        refetch?.();
      } catch (error) {
        handleApiError(error, 'Failed to decline club request');
      }
    },
    [declineClubRequestCreation, handleApiError, refetch, toast]
  );

  return {
    isOpen,
    isUnflagging: isUnflaggingPost || isUnflaggingEvent,
    isRemoving: isRemovingPost || isRemovingEvent,
    isDecliningClubRequest,
    isMarkingAsRead,

    setIsOpen,
    handleUnflagNotification,
    handleRemoveNotification,
    handleNavigate,
    handleApproveClubRequest,
    handleDeclineClubRequest,
    handleMarkAsRead,
  };
};

export default useNotificationActions;
