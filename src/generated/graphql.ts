import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = {
  [_ in K]?: never;
};
export type Incremental<T> =
  | T
  | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Date: { input: any; output: any };
  DateTime: { input: any; output: any };
};

export type AccessRequest = {
  __typename?: 'AccessRequest';
  association: Association;
  city: Scalars['String']['output'];
  contact?: Maybe<Contact>;
  createdAt: Scalars['DateTime']['output'];
  dob?: Maybe<Scalars['Date']['output']>;
  email: Scalars['String']['output'];
  firstName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  lastName: Scalars['String']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  state: Scalars['String']['output'];
  street: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  zipCode: Scalars['String']['output'];
};

export type AccessRequestFilterInput = {
  endDate?: InputMaybe<Scalars['String']['input']>;
  hasContact?: InputMaybe<Scalars['Boolean']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};

export type Address = {
  __typename?: 'Address';
  association?: Maybe<Association>;
  city?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  isActive: Scalars['Boolean']['output'];
  isPrimaryUnit: Scalars['Boolean']['output'];
  isSameAsUnitAddress?: Maybe<Scalars['Boolean']['output']>;
  source?: Maybe<AddressSource>;
  state?: Maybe<Scalars['String']['output']>;
  street?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  user?: Maybe<User>;
  zipCode?: Maybe<Scalars['String']['output']>;
};

export enum AddressSource {
  CoOwnerProperty = 'CO_OWNER_PROPERTY',
  Custom = 'CUSTOM',
  Mailing = 'MAILING',
  OwnerProperty = 'OWNER_PROPERTY',
}

export type AddressesByUserIdInput = {
  userId: Scalars['ID']['input'];
};

export type AdminAssociation = {
  __typename?: 'AdminAssociation';
  activeClubCount?: Maybe<Scalars['Int']['output']>;
  canUseClubs?: Maybe<Scalars['Boolean']['output']>;
  clubMemberCount?: Maybe<Scalars['Int']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  memberCount?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type AdminAssociationsFilterInput = {
  name?: InputMaybe<Scalars['String']['input']>;
};

export enum AdminAssociationsOrderByField {
  ClubMemberCount = 'CLUB_MEMBER_COUNT',
  MemberCount = 'MEMBER_COUNT',
  Name = 'NAME',
  UpdatedAt = 'UPDATED_AT',
}

export type AdminAssociationsOrderInput = {
  direction?: InputMaybe<OrderDirection>;
  field: AdminAssociationsOrderByField;
};

export type AdminClub = {
  __typename?: 'AdminClub';
  activatedAt?: Maybe<Scalars['DateTime']['output']>;
  clubTemplate?: Maybe<ClubTemplate>;
  id: Scalars['ID']['output'];
  lastActivity?: Maybe<Scalars['DateTime']['output']>;
  memberCount?: Maybe<Scalars['Int']['output']>;
};

export type AdminClubEvent = {
  __typename?: 'AdminClubEvent';
  clubId?: Maybe<Scalars['ID']['output']>;
  clubProfile?: Maybe<AdminClubProfile>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  location?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  reactionCount?: Maybe<Scalars['Int']['output']>;
  reports?: Maybe<Array<ClubReport>>;
  startTime?: Maybe<Scalars['DateTime']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type AdminClubEventsFilterInput = {
  eventsDateFrom?: InputMaybe<Scalars['Date']['input']>;
  eventsDateTo?: InputMaybe<Scalars['Date']['input']>;
  isDeleted?: InputMaybe<Scalars['Boolean']['input']>;
  onlyFlagged?: InputMaybe<Scalars['Boolean']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<AdminEventStatus>;
  userId?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminClubMembership = {
  __typename?: 'AdminClubMembership';
  clubProfile: AdminClubProfile;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  joinedAt: Scalars['DateTime']['output'];
  status: MembershipStatusEnum;
};

export type AdminClubPost = {
  __typename?: 'AdminClubPost';
  clubId: Scalars['ID']['output'];
  clubProfile: AdminClubProfile;
  content?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  isPinned?: Maybe<Scalars['Boolean']['output']>;
  reactionCount?: Maybe<Scalars['Int']['output']>;
  reactions?: Maybe<Array<ClubPostReaction>>;
  reports?: Maybe<Array<ClubReport>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type AdminClubPostsFilterInput = {
  isDeleted?: InputMaybe<Scalars['Boolean']['input']>;
  onlyFlagged?: InputMaybe<Scalars['Boolean']['input']>;
  postsDateFrom?: InputMaybe<Scalars['Date']['input']>;
  postsDateTo?: InputMaybe<Scalars['Date']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminClubProfile = {
  __typename?: 'AdminClubProfile';
  createdAt: Scalars['DateTime']['output'];
  displayName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  img?: Maybe<UploadFile>;
  updatedAt: Scalars['DateTime']['output'];
  user?: Maybe<User>;
};

export enum AdminEventStatus {
  Past = 'PAST',
  Upcoming = 'UPCOMING',
}

export type AdminNotification = {
  __typename?: 'AdminNotification';
  action?: Maybe<AdminReportActionType>;
  clubEvent?: Maybe<AdminClubEvent>;
  clubPost?: Maybe<AdminClubPost>;
  clubRequest?: Maybe<ClubRequest>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  isRead: Scalars['Boolean']['output'];
  payload?: Maybe<AdminNotificationPayload>;
  reportedAt: Scalars['DateTime']['output'];
  type: AdminNotificationType;
  updatedAt: Scalars['DateTime']['output'];
};

export type AdminNotificationPayload = {
  __typename?: 'AdminNotificationPayload';
  clubRequestId?: Maybe<Scalars['String']['output']>;
  eventId?: Maybe<Scalars['String']['output']>;
  postId?: Maybe<Scalars['String']['output']>;
};

export enum AdminNotificationType {
  ClubCreationRequest = 'CLUB_CREATION_REQUEST',
  ClubEventReport = 'CLUB_EVENT_REPORT',
  ClubPostReport = 'CLUB_POST_REPORT',
}

export type AdminRemoveUserInput = {
  userId: Scalars['String']['input'];
};

export enum AdminReportActionType {
  Approved = 'APPROVED',
  Declined = 'DECLINED',
  Delete = 'DELETE',
  Unflag = 'UNFLAG',
}

export type AdminUpdatePasswordInput = {
  newPassword: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type AdminUpdateProfileInput = {
  associationId?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  role?: InputMaybe<UserRole>;
  userId: Scalars['String']['input'];
};

export type AppVersion = {
  __typename?: 'AppVersion';
  currentVersion: Scalars['String']['output'];
  minSupportedVersion: Scalars['String']['output'];
};

export type AssignContactInput = {
  accessRequestId: Scalars['ID']['input'];
  salesforceContactId: Scalars['ID']['input'];
};

export type AssignContactToAccessValidationInput = {
  caseId: Scalars['String']['input'];
  contactId: Scalars['String']['input'];
};

export type AssignContactToAccessValidationResult = {
  __typename?: 'AssignContactToAccessValidationResult';
  error?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Association = {
  __typename?: 'Association';
  canUseClubs?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type AssociationEvent = {
  __typename?: 'AssociationEvent';
  about?: Maybe<Scalars['String']['output']>;
  coordinates?: Maybe<Scalars['String']['output']>;
  endsAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  isAttending?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  startsAt?: Maybe<Scalars['DateTime']['output']>;
  title: Scalars['String']['output'];
};

export type AssociationsByUserIdInput = {
  userId: Scalars['String']['input'];
};

export type AuthToken = {
  __typename?: 'AuthToken';
  accessToken: Scalars['String']['output'];
  accessTokenExpireTime: Scalars['Int']['output'];
  refreshToken: Scalars['String']['output'];
  refreshTokenExpireTime: Scalars['Int']['output'];
  role?: Maybe<Scalars['String']['output']>;
};

export type BanClubMemberInput = {
  clubTemplateId: Scalars['ID']['input'];
  membershipId: Scalars['ID']['input'];
};

export enum BirthdayMonth {
  April = 'April',
  August = 'August',
  December = 'December',
  February = 'February',
  January = 'January',
  July = 'July',
  June = 'June',
  March = 'March',
  May = 'May',
  November = 'November',
  October = 'October',
  September = 'September',
}

export type ClubActionResponse = {
  __typename?: 'ClubActionResponse';
  clubTemplateId: Scalars['ID']['output'];
  membershipId?: Maybe<Scalars['ID']['output']>;
};

export enum ClubCategoryEnum {
  Creative = 'CREATIVE',
  FitnessOutdoor = 'FITNESS_OUTDOOR',
  FoodDrink = 'FOOD_DRINK',
  Hobbies = 'HOBBIES',
  SocialFamily = 'SOCIAL_FAMILY',
}

export type ClubCategoryStats = {
  __typename?: 'ClubCategoryStats';
  activeClubs?: Maybe<Scalars['Int']['output']>;
  category: ClubCategoryEnum;
  memberCount?: Maybe<Scalars['Int']['output']>;
};

export type ClubEvent = {
  __typename?: 'ClubEvent';
  clubProfile?: Maybe<ClubProfile>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['DateTime']['output']>;
  hasReacted?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  isNew?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  reactionCount?: Maybe<Scalars['Int']['output']>;
  startTime?: Maybe<Scalars['DateTime']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type ClubEventReaction = {
  __typename?: 'ClubEventReaction';
  clubProfileId: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  eventId: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
};

export type ClubFilterInput = {
  category?: InputMaybe<ClubCategoryEnum>;
  lastActivityDateFrom?: InputMaybe<Scalars['Date']['input']>;
  lastActivityDateTo?: InputMaybe<Scalars['Date']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type ClubMembersFilterInput = {
  dateJoinedFrom?: InputMaybe<Scalars['Date']['input']>;
  dateJoinedTo?: InputMaybe<Scalars['Date']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<MembershipStatusEnum>;
};

export enum ClubMembersOrderByEnum {
  Id = 'ID',
  JoinedDate = 'JOINED_DATE',
  Name = 'NAME',
}

export type ClubMembersOrderInput = {
  direction?: InputMaybe<OrderDirection>;
  field?: InputMaybe<ClubMembersOrderByEnum>;
};

export type ClubMembership = {
  __typename?: 'ClubMembership';
  clubProfile: ClubProfile;
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  joinedAt: Scalars['DateTime']['output'];
  status: MembershipStatusEnum;
};

export enum ClubOrderByEnum {
  ActivatedDate = 'ACTIVATED_DATE',
  LastActivity = 'LAST_ACTIVITY',
  Name = 'NAME',
  UpdatedAt = 'UPDATED_AT',
}

export type ClubOrderInput = {
  direction?: InputMaybe<OrderDirection>;
  field?: InputMaybe<ClubOrderByEnum>;
};

export type ClubPost = {
  __typename?: 'ClubPost';
  clubId: Scalars['ID']['output'];
  clubProfile: ClubProfile;
  content?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  hasReacted?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  isNew?: Maybe<Scalars['Boolean']['output']>;
  isPinned?: Maybe<Scalars['Boolean']['output']>;
  reactionCount?: Maybe<Scalars['Int']['output']>;
  reactions?: Maybe<Array<ClubPostReaction>>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type ClubPostReaction = {
  __typename?: 'ClubPostReaction';
  clubProfileId: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  postId: Scalars['ID']['output'];
};

export type ClubProfile = {
  __typename?: 'ClubProfile';
  displayName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  img?: Maybe<UploadFile>;
};

export type ClubReport = {
  __typename?: 'ClubReport';
  category?: Maybe<ReportCategory>;
  clubEvent?: Maybe<ClubEvent>;
  clubPost?: Maybe<ClubPost>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  details?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  reporter: AdminClubProfile;
  status: ReportStatusEnum;
};

export type ClubReportsFilterInput = {
  categoryId?: InputMaybe<Scalars['ID']['input']>;
  clubEventId?: InputMaybe<Scalars['ID']['input']>;
  clubPostId?: InputMaybe<Scalars['ID']['input']>;
  createdAtFrom?: InputMaybe<Scalars['DateTime']['input']>;
  createdAtTo?: InputMaybe<Scalars['DateTime']['input']>;
  reporterId?: InputMaybe<Scalars['ID']['input']>;
  status?: InputMaybe<ReportStatusEnum>;
};

export enum ClubReportsOrderByEnum {
  CreatedAt = 'CREATED_AT',
  Status = 'STATUS',
}

export type ClubReportsOrderInput = {
  direction?: InputMaybe<OrderDirection>;
  field?: InputMaybe<ClubReportsOrderByEnum>;
};

export type ClubRequest = {
  __typename?: 'ClubRequest';
  category?: Maybe<ClubCategoryEnum>;
  clubAbout?: Maybe<Scalars['String']['output']>;
  clubDescription: Scalars['String']['output'];
  clubName: Scalars['String']['output'];
  clubProfile?: Maybe<ClubProfile>;
  createdAt: Scalars['Date']['output'];
  id: Scalars['ID']['output'];
  status: ClubRequestStatus;
  user?: Maybe<User>;
};

export type ClubRequestFilterInput = {
  category?: InputMaybe<ClubCategoryEnum>;
  search?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<ClubRequestStatus>;
  userId?: InputMaybe<Scalars['ID']['input']>;
};

export enum ClubRequestOrderByField {
  CreatedAt = 'CREATED_AT',
  Status = 'STATUS',
}

export type ClubRequestOrderInput = {
  direction?: InputMaybe<OrderDirection>;
  field?: InputMaybe<ClubRequestOrderByField>;
};

export enum ClubRequestStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED',
}

export type ClubTemplate = {
  __typename?: 'ClubTemplate';
  about?: Maybe<Scalars['String']['output']>;
  category?: Maybe<ClubCategoryEnum>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  hasJoined?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  img?: Maybe<UploadFile>;
  memberCount?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  newPost?: Maybe<Scalars['Boolean']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type ClubTemplatesFilterInput = {
  category?: InputMaybe<ClubCategoryEnum>;
  dateUpdatedFrom?: InputMaybe<Scalars['Date']['input']>;
  dateUpdatedTo?: InputMaybe<Scalars['Date']['input']>;
  isActivated?: InputMaybe<Scalars['Boolean']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type CompleteSignupInput = {
  address?: InputMaybe<UpdateAddressInput>;
  dob?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  newPassword: Scalars['String']['input'];
  otp: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type Contact = {
  __typename?: 'Contact';
  id: Scalars['ID']['output'];
  salesforceId: Scalars['String']['output'];
};

export type ContactUs = {
  __typename?: 'ContactUs';
  description: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  firstName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  lastName: Scalars['String']['output'];
  phone: Scalars['String']['output'];
};

export enum ContactUsType {
  Payment = 'PAYMENT',
  Profile = 'PROFILE',
  Request = 'REQUEST',
}

export type ContinueSignupInput = {
  email: Scalars['String']['input'];
  otp: Scalars['String']['input'];
};

export type CreateAccessRequestInput = {
  associationId: Scalars['ID']['input'];
  city: Scalars['String']['input'];
  dob?: InputMaybe<Scalars['Date']['input']>;
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  state: Scalars['String']['input'];
  street: Scalars['String']['input'];
  zipCode: Scalars['String']['input'];
};

export type CreateAddressInput = {
  city: Scalars['String']['input'];
  state: Scalars['String']['input'];
  street: Scalars['String']['input'];
  userId: Scalars['ID']['input'];
  zipCode: Scalars['String']['input'];
};

export type CreateClubEventInput = {
  clubTemplateId: Scalars['ID']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  endTime: Scalars['DateTime']['input'];
  location: Scalars['String']['input'];
  name: Scalars['String']['input'];
  startTime: Scalars['DateTime']['input'];
};

export type CreateClubPostInput = {
  clubId: Scalars['ID']['input'];
  content: Scalars['String']['input'];
};

export type CreateClubProfileInput = {
  displayName: Scalars['String']['input'];
  imgId?: InputMaybe<Scalars['ID']['input']>;
};

export type CreateClubReportInput = {
  categoryId?: InputMaybe<Scalars['ID']['input']>;
  clubEventId?: InputMaybe<Scalars['ID']['input']>;
  clubPostId?: InputMaybe<Scalars['ID']['input']>;
  details?: InputMaybe<Scalars['String']['input']>;
};

export type CreateClubRequestInput = {
  category?: InputMaybe<ClubCategoryEnum>;
  clubAbout?: InputMaybe<Scalars['String']['input']>;
  clubDescription: Scalars['String']['input'];
  clubName: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type CreateClubTemplateInput = {
  about?: InputMaybe<Scalars['String']['input']>;
  category?: InputMaybe<ClubCategoryEnum>;
  description?: InputMaybe<Scalars['String']['input']>;
  imgId?: InputMaybe<Scalars['ID']['input']>;
  name: Scalars['String']['input'];
};

export type CreatePaymentContactUsInput = {
  description: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreateProfileContactUsInput = {
  description: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreateReportCategoryInput = {
  code: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  ordering?: InputMaybe<Scalars['Int']['input']>;
  title: Scalars['String']['input'];
};

export type CreateRequestContactUsInput = {
  description: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreateRequestInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  attachments?: InputMaybe<Array<Scalars['String']['input']>>;
  category: RequestCategory;
  completionDate?: InputMaybe<Scalars['String']['input']>;
  contractorName?: InputMaybe<Scalars['String']['input']>;
  contractorPhone?: InputMaybe<Scalars['String']['input']>;
  coordinates?: InputMaybe<Array<Scalars['Float']['input']>>;
  denialReason?: InputMaybe<Scalars['String']['input']>;
  description: Scalars['String']['input'];
  projectName?: InputMaybe<Scalars['String']['input']>;
  response?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type CreateScreeningKeywordInput = {
  keyword: Scalars['String']['input'];
};

export type CreateUploadFileInput = {
  filename: Scalars['String']['input'];
  mimeType: Scalars['String']['input'];
  size?: InputMaybe<Scalars['Int']['input']>;
};

export type DocumentFile = DocumentItem & {
  __typename?: 'DocumentFile';
  boxCreatedAt?: Maybe<Scalars['Date']['output']>;
  boxModifiedAt?: Maybe<Scalars['Date']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  downloadUrl?: Maybe<Scalars['String']['output']>;
  extension?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  itemType: DocumentItemType;
  mimeType?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pathCollection?: Maybe<Scalars['String']['output']>;
  sharedLink?: Maybe<Scalars['String']['output']>;
  size?: Maybe<Scalars['Float']['output']>;
  thumbnailUrl?: Maybe<Scalars['String']['output']>;
};

export type DocumentFolder = DocumentItem & {
  __typename?: 'DocumentFolder';
  boxCreatedAt?: Maybe<Scalars['Date']['output']>;
  boxModifiedAt?: Maybe<Scalars['Date']['output']>;
  children?: Maybe<Array<Maybe<DocumentItemUnion>>>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  itemType: DocumentItemType;
  name?: Maybe<Scalars['String']['output']>;
  pathCollection?: Maybe<Scalars['String']['output']>;
};

export type DocumentItem = {
  boxCreatedAt?: Maybe<Scalars['Date']['output']>;
  boxModifiedAt?: Maybe<Scalars['Date']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  itemType: DocumentItemType;
  name?: Maybe<Scalars['String']['output']>;
  pathCollection?: Maybe<Scalars['String']['output']>;
};

export enum DocumentItemType {
  File = 'FILE',
  Folder = 'FOLDER',
}

export type DocumentItemUnion = DocumentFile | DocumentFolder;

export type DocumentsInput = {
  parentId?: InputMaybe<Scalars['String']['input']>;
};

export type EditClubPostInput = {
  content: Scalars['String']['input'];
  postId: Scalars['ID']['input'];
};

export type EditScreeningKeywordInput = {
  id: Scalars['ID']['input'];
  keyword?: InputMaybe<Scalars['String']['input']>;
};

export type Faq = {
  __typename?: 'Faq';
  answer: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  ordering: Scalars['Int']['output'];
  question: Scalars['String']['output'];
  type?: Maybe<FaqTypeEnum>;
  updatedAt: Scalars['DateTime']['output'];
};

export enum FaqTypeEnum {
  General = 'GENERAL',
  Payment = 'PAYMENT',
}

export type ForgotPasswordInput = {
  email: Scalars['String']['input'];
};

export type InvitationError = {
  __typename?: 'InvitationError';
  contactId: Scalars['String']['output'];
  message: Scalars['String']['output'];
};

export type InvitationResult = {
  __typename?: 'InvitationResult';
  error: Array<InvitationError>;
  success: Array<Scalars['String']['output']>;
};

export type JoinClubInput = {
  clubTemplateId: Scalars['ID']['input'];
};

export type LeaveClubInput = {
  clubTemplateId: Scalars['ID']['input'];
};

export type LoginInput = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type MarkNotificationsAsReadInput = {
  all?: InputMaybe<Scalars['Boolean']['input']>;
  notificationIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export enum MembershipStatusEnum {
  Active = 'ACTIVE',
  Banned = 'BANNED',
  Inactive = 'INACTIVE',
}

export type Message = {
  __typename?: 'Message';
  message: Scalars['String']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  adminDeleteClubEvent: ClubEvent;
  adminRemoveClubPostById: ClubPost;
  adminRemoveUser: Message;
  adminSendSignupEmails: Message;
  adminUnflagReportsByEventId: ClubEvent;
  adminUnflagReportsByPostId: ClubPost;
  adminUpdatePassword: Message;
  adminUpdateProfile: User;
  approvedClubRequestCreation: ClubRequest;
  assignContactToAccessRequest: AccessRequest;
  banClubMember: ClubActionResponse;
  completeSignup: AuthToken;
  createAccessRequest: AccessRequest;
  createClubEvent: ClubEvent;
  createClubPost: ClubPost;
  createClubProfile: ClubProfile;
  createClubReport: ClubReport;
  createClubRequest: ClubRequest;
  createClubTemplate: ClubTemplate;
  createFaq: Faq;
  createPaymentContactUs: ContactUs;
  createProfileContactUs: ContactUs;
  createReportCategory: ReportCategory;
  createRequest: Request;
  createRequestContactUs: ContactUs;
  createScreeningKeyword: ScreeningKeyword;
  createUploadUrl: UploadUrlResponse;
  deleteAccount: Message;
  deleteAllReportByEventId: ClubEvent;
  deleteAllReportByPostId: ClubPost;
  deleteClubEvent: Scalars['Boolean']['output'];
  deleteClubTemplate: Scalars['Boolean']['output'];
  deleteFaq: Scalars['Boolean']['output'];
  deleteReportCategory: Scalars['Boolean']['output'];
  editClubPost: ClubPost;
  editScreeningKeyword: ScreeningKeyword;
  forgotPassword: Message;
  joinClub: ClubActionResponse;
  leaveClub: ClubActionResponse;
  login: AuthToken;
  markAdminNotificationsAsRead: Scalars['Boolean']['output'];
  markClubEventAsRead: ClubEvent;
  markClubPostAsRead: ClubPost;
  markHasSeenComposeDisclaimer: User;
  markNotificationsAsRead: Scalars['Boolean']['output'];
  reactToClubEvent: ClubEventReaction;
  reactToClubPost: ClubPostReaction;
  refreshToken: AuthToken;
  register: Message;
  registerCheckAddress: Otp;
  registerCheckEmail: RegisterCheckEmailInputResponse;
  registerCheckInviteCode: Otp;
  registerCheckSignupEmail: Otp;
  registerDevice: Scalars['Boolean']['output'];
  rejectedClubRequestCreation: ClubRequest;
  removeClubMember: ClubActionResponse;
  removeClubPostById: ClubPost;
  removeScreeningKeyword: ScreeningKeyword;
  requestEmailChange: Message;
  requestTransactionHistoryExport: TransactionHistoryExportResponse;
  resetPassword: Message;
  resubscribeEmail: Message;
  setActiveAddress: Address;
  setAdminNotificationAction: Scalars['Boolean']['output'];
  setPrimaryUnit: Unit;
  sfAssignContactToAccessValidation: AssignContactToAccessValidationResult;
  sfSendInvitations: InvitationResult;
  toggleAssociationClubFeature: Association;
  toggleNewsReaction: News;
  togglePinClubPost: ClubPost;
  toggleSameAsUnitAddress: Address;
  toggleUserClubFeature: User;
  unbanClubMember: ClubActionResponse;
  unregisterDevice: Scalars['Boolean']['output'];
  unsubscribeEmail: Message;
  updateAddress: Address;
  updateAppVersion: AppVersion;
  updateClubEvent: ClubEvent;
  updateClubProfile: ClubProfile;
  updateClubRequest: ClubRequest;
  updateClubTemplate: ClubTemplate;
  updateFaq: Faq;
  updatePassword: Message;
  updateProfile: User;
  updateReportCategory: ReportCategory;
  updateUserSettings: UserSettings;
  verifyEmailChange: Message;
};

export type MutationAdminDeleteClubEventArgs = {
  clubEventId: Scalars['ID']['input'];
};

export type MutationAdminRemoveClubPostByIdArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationAdminRemoveUserArgs = {
  input: AdminRemoveUserInput;
};

export type MutationAdminSendSignupEmailsArgs = {
  input: SendSignupEmailsInput;
};

export type MutationAdminUnflagReportsByEventIdArgs = {
  eventId: Scalars['ID']['input'];
};

export type MutationAdminUnflagReportsByPostIdArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationAdminUpdatePasswordArgs = {
  input: AdminUpdatePasswordInput;
};

export type MutationAdminUpdateProfileArgs = {
  input: AdminUpdateProfileInput;
};

export type MutationApprovedClubRequestCreationArgs = {
  clubRequestId: Scalars['ID']['input'];
  clubTemplateId: Scalars['ID']['input'];
};

export type MutationAssignContactToAccessRequestArgs = {
  input: AssignContactInput;
};

export type MutationBanClubMemberArgs = {
  input: BanClubMemberInput;
};

export type MutationCompleteSignupArgs = {
  input: CompleteSignupInput;
};

export type MutationCreateAccessRequestArgs = {
  input: CreateAccessRequestInput;
};

export type MutationCreateClubEventArgs = {
  input: CreateClubEventInput;
};

export type MutationCreateClubPostArgs = {
  input: CreateClubPostInput;
};

export type MutationCreateClubProfileArgs = {
  input: CreateClubProfileInput;
};

export type MutationCreateClubReportArgs = {
  input: CreateClubReportInput;
};

export type MutationCreateClubRequestArgs = {
  input: CreateClubRequestInput;
};

export type MutationCreateClubTemplateArgs = {
  input: CreateClubTemplateInput;
};

export type MutationCreateFaqArgs = {
  answer: Scalars['String']['input'];
  ordering?: InputMaybe<Scalars['Int']['input']>;
  question: Scalars['String']['input'];
  type?: InputMaybe<FaqTypeEnum>;
};

export type MutationCreatePaymentContactUsArgs = {
  input: CreatePaymentContactUsInput;
};

export type MutationCreateProfileContactUsArgs = {
  input: CreateProfileContactUsInput;
};

export type MutationCreateReportCategoryArgs = {
  input: CreateReportCategoryInput;
};

export type MutationCreateRequestArgs = {
  input: CreateRequestInput;
};

export type MutationCreateRequestContactUsArgs = {
  input: CreateRequestContactUsInput;
};

export type MutationCreateScreeningKeywordArgs = {
  input: CreateScreeningKeywordInput;
};

export type MutationCreateUploadUrlArgs = {
  input: CreateUploadFileInput;
};

export type MutationDeleteAllReportByEventIdArgs = {
  eventId: Scalars['ID']['input'];
};

export type MutationDeleteAllReportByPostIdArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationDeleteClubEventArgs = {
  clubEventId: Scalars['ID']['input'];
};

export type MutationDeleteClubTemplateArgs = {
  id: Scalars['ID']['input'];
};

export type MutationDeleteFaqArgs = {
  id: Scalars['ID']['input'];
};

export type MutationDeleteReportCategoryArgs = {
  id: Scalars['ID']['input'];
};

export type MutationEditClubPostArgs = {
  input: EditClubPostInput;
};

export type MutationEditScreeningKeywordArgs = {
  input: EditScreeningKeywordInput;
};

export type MutationForgotPasswordArgs = {
  input: ForgotPasswordInput;
};

export type MutationJoinClubArgs = {
  input: JoinClubInput;
};

export type MutationLeaveClubArgs = {
  input: LeaveClubInput;
};

export type MutationLoginArgs = {
  input: LoginInput;
};

export type MutationMarkAdminNotificationsAsReadArgs = {
  input: MarkNotificationsAsReadInput;
};

export type MutationMarkClubEventAsReadArgs = {
  clubEventId: Scalars['ID']['input'];
};

export type MutationMarkClubPostAsReadArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationMarkNotificationsAsReadArgs = {
  input: MarkNotificationsAsReadInput;
};

export type MutationReactToClubEventArgs = {
  clubEventId: Scalars['ID']['input'];
};

export type MutationReactToClubPostArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationRefreshTokenArgs = {
  input: RefreshTokenInput;
};

export type MutationRegisterArgs = {
  input: RegisterInput;
};

export type MutationRegisterCheckAddressArgs = {
  input: RegisterCheckAddressInput;
};

export type MutationRegisterCheckEmailArgs = {
  input: RegisterCheckEmailInput;
};

export type MutationRegisterCheckInviteCodeArgs = {
  input: RegisterCheckInviteCodeInput;
};

export type MutationRegisterCheckSignupEmailArgs = {
  input: RegisterCheckEmailInput;
};

export type MutationRegisterDeviceArgs = {
  input: RegisterDeviceInput;
};

export type MutationRejectedClubRequestCreationArgs = {
  clubRequestId: Scalars['ID']['input'];
};

export type MutationRemoveClubMemberArgs = {
  input: RemoveClubMemberInput;
};

export type MutationRemoveClubPostByIdArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationRemoveScreeningKeywordArgs = {
  keywordId: Scalars['ID']['input'];
};

export type MutationRequestEmailChangeArgs = {
  input: RequestEmailChangeInput;
};

export type MutationRequestTransactionHistoryExportArgs = {
  input: RequestTransactionHistoryExportInput;
};

export type MutationResetPasswordArgs = {
  input: ResetPasswordInput;
};

export type MutationResubscribeEmailArgs = {
  input: ResubscribeInput;
};

export type MutationSetActiveAddressArgs = {
  setActiveAddressInput: SetActiveAddressInput;
};

export type MutationSetAdminNotificationActionArgs = {
  input: SetAdminNotificationActionInput;
};

export type MutationSetPrimaryUnitArgs = {
  setPrimaryUnitInput: SetPrimaryUnitInput;
};

export type MutationSfAssignContactToAccessValidationArgs = {
  input: AssignContactToAccessValidationInput;
};

export type MutationSfSendInvitationsArgs = {
  input: SendInvitationsInput;
};

export type MutationToggleAssociationClubFeatureArgs = {
  associationId: Scalars['ID']['input'];
};

export type MutationToggleNewsReactionArgs = {
  input: ToggleNewsReactionInput;
};

export type MutationTogglePinClubPostArgs = {
  postId: Scalars['ID']['input'];
};

export type MutationToggleSameAsUnitAddressArgs = {
  toggleSameAsUnitAddressInput: ToggleSameAsUnitAddressInput;
};

export type MutationToggleUserClubFeatureArgs = {
  userId: Scalars['ID']['input'];
};

export type MutationUnbanClubMemberArgs = {
  input: UnbanClubMemberInput;
};

export type MutationUnregisterDeviceArgs = {
  input: RegisterDeviceInput;
};

export type MutationUnsubscribeEmailArgs = {
  input: UnsubscribeInput;
};

export type MutationUpdateAddressArgs = {
  updateAddressInput: UpdateAddressInput;
};

export type MutationUpdateAppVersionArgs = {
  input: UpdateAppVersionInput;
};

export type MutationUpdateClubEventArgs = {
  input: UpdateClubEventInput;
};

export type MutationUpdateClubProfileArgs = {
  input: UpdateClubProfileInput;
};

export type MutationUpdateClubRequestArgs = {
  input: UpdateClubRequestInput;
};

export type MutationUpdateClubTemplateArgs = {
  input: UpdateClubTemplateInput;
};

export type MutationUpdateFaqArgs = {
  answer?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  ordering?: InputMaybe<Scalars['Int']['input']>;
  question?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<FaqTypeEnum>;
};

export type MutationUpdatePasswordArgs = {
  input: UpdatePasswordInput;
};

export type MutationUpdateProfileArgs = {
  input: UpdateProfileInput;
};

export type MutationUpdateReportCategoryArgs = {
  id: Scalars['ID']['input'];
  input: UpdateReportCategoryInput;
};

export type MutationUpdateUserSettingsArgs = {
  input: UpdateUserSettingsInput;
};

export type MutationVerifyEmailChangeArgs = {
  input: VerifyEmailChangeInput;
};

export type MyAssociationsInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  otp?: InputMaybe<Scalars['String']['input']>;
};

export type News = {
  __typename?: 'News';
  content: Scalars['String']['output'];
  date: Scalars['Date']['output'];
  hasReacted: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
  reactionCount: Scalars['Int']['output'];
  title?: Maybe<Scalars['String']['output']>;
};

export type NewsOrderInput = {
  direction?: InputMaybe<OrderDirection>;
  field?: InputMaybe<Scalars['String']['input']>;
};

export type Notification = {
  __typename?: 'Notification';
  clubPost?: Maybe<ClubPost>;
  content: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  deeplink?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isRead: Scalars['Boolean']['output'];
  payload?: Maybe<NotificationPayload>;
  type: NotificationType;
  updatedAt: Scalars['DateTime']['output'];
};

export type NotificationPayload = {
  __typename?: 'NotificationPayload';
  clubTemplateId?: Maybe<Scalars['String']['output']>;
  eventId?: Maybe<Scalars['String']['output']>;
  fcmMessageIds?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  newsId?: Maybe<Scalars['String']['output']>;
  postId?: Maybe<Scalars['String']['output']>;
  requestId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export enum NotificationType {
  ClubAccess = 'CLUB_ACCESS',
  ClubActivated = 'CLUB_ACTIVATED',
  ClubEvent = 'CLUB_EVENT',
  ClubEventRemoved = 'CLUB_EVENT_REMOVED',
  ClubPost = 'CLUB_POST',
  ClubPostRemoved = 'CLUB_POST_REMOVED',
  CommunityNews = 'COMMUNITY_NEWS',
  EventReminder = 'EVENT_REMINDER',
  RequestStatus = 'REQUEST_STATUS',
}

export enum OrderDirection {
  Asc = 'ASC',
  Desc = 'DESC',
}

export type Otp = {
  __typename?: 'Otp';
  email: Scalars['String']['output'];
  otp: Scalars['String']['output'];
};

export enum OtpType {
  ChangeEmail = 'CHANGE_EMAIL',
  ForgotPassword = 'FORGOT_PASSWORD',
  Signup = 'SIGNUP',
  VerifyEmail = 'VERIFY_EMAIL',
}

export type PaginatedAccessRequestResponse = {
  __typename?: 'PaginatedAccessRequestResponse';
  items: Array<AccessRequest>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAdminAssociationResponse = {
  __typename?: 'PaginatedAdminAssociationResponse';
  items: Array<AdminAssociation>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAdminClubEventsResponse = {
  __typename?: 'PaginatedAdminClubEventsResponse';
  items: Array<AdminClubEvent>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAdminClubMembersResponse = {
  __typename?: 'PaginatedAdminClubMembersResponse';
  items: Array<AdminClubMembership>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAdminClubPostsResponse = {
  __typename?: 'PaginatedAdminClubPostsResponse';
  items: Array<AdminClubPost>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAdminClubResponse = {
  __typename?: 'PaginatedAdminClubResponse';
  items: Array<AdminClub>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAdminNotificationsResponse = {
  __typename?: 'PaginatedAdminNotificationsResponse';
  items: Array<AdminNotification>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedAssociationResponse = {
  __typename?: 'PaginatedAssociationResponse';
  items: Array<Association>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubCategoriesResponse = {
  __typename?: 'PaginatedClubCategoriesResponse';
  items: Array<ClubCategoryStats>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubEventsResponse = {
  __typename?: 'PaginatedClubEventsResponse';
  items: Array<ClubEvent>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubMembersResponse = {
  __typename?: 'PaginatedClubMembersResponse';
  items: Array<ClubMembership>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubPostsResponse = {
  __typename?: 'PaginatedClubPostsResponse';
  items: Array<ClubPost>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubReportsResponse = {
  __typename?: 'PaginatedClubReportsResponse';
  items: Array<ClubReport>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubRequestsResponse = {
  __typename?: 'PaginatedClubRequestsResponse';
  items: Array<ClubRequest>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedClubTemplateResponse = {
  __typename?: 'PaginatedClubTemplateResponse';
  items: Array<ClubTemplate>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedEventResponse = {
  __typename?: 'PaginatedEventResponse';
  items: Array<AssociationEvent>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedFaqResponse = {
  __typename?: 'PaginatedFaqResponse';
  items: Array<Faq>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedNewsResponse = {
  __typename?: 'PaginatedNewsResponse';
  items: Array<News>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedNotificationsResponse = {
  __typename?: 'PaginatedNotificationsResponse';
  items: Array<Notification>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedRequestResponse = {
  __typename?: 'PaginatedRequestResponse';
  items: Array<PaginatedRequestResponseItem>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedRequestResponseItem = {
  __typename?: 'PaginatedRequestResponseItem';
  address?: Maybe<Scalars['String']['output']>;
  attachments?: Maybe<Array<Scalars['String']['output']>>;
  category: RequestCategory;
  completionDate?: Maybe<Scalars['Date']['output']>;
  contractorName?: Maybe<Scalars['String']['output']>;
  contractorPhone?: Maybe<Scalars['String']['output']>;
  coordinates?: Maybe<Array<Maybe<Scalars['Float']['output']>>>;
  denialReason?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  projectName?: Maybe<Scalars['String']['output']>;
  response?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
  status: RequestStatus;
  type?: Maybe<Scalars['String']['output']>;
};

export type PaginatedScreeningKeywordsResponse = {
  __typename?: 'PaginatedScreeningKeywordsResponse';
  items: Array<ScreeningKeyword>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedTransactionHistoryResponse = {
  __typename?: 'PaginatedTransactionHistoryResponse';
  items: Array<TransactionHistory>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUnitResponse = {
  __typename?: 'PaginatedUnitResponse';
  items: Array<Unit>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUserResponse = {
  __typename?: 'PaginatedUserResponse';
  items: Array<User>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUsersCreateEventsResponse = {
  __typename?: 'PaginatedUsersCreateEventsResponse';
  items: Array<User>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUsersCreatePostResponse = {
  __typename?: 'PaginatedUsersCreatePostResponse';
  items: Array<User>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginatedUsersRequestedClubCreationResponse = {
  __typename?: 'PaginatedUsersRequestedClubCreationResponse';
  items: Array<User>;
  limit: Scalars['Int']['output'];
  page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type PaginationArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type PresignedUrl = {
  __typename?: 'PresignedUrl';
  fields: PresignedUrlFields;
  url: Scalars['String']['output'];
};

export type PresignedUrlFields = {
  __typename?: 'PresignedUrlFields';
  Policy: Scalars['String']['output'];
  X_Amz_Algorithm: Scalars['String']['output'];
  X_Amz_Credential: Scalars['String']['output'];
  X_Amz_Date: Scalars['String']['output'];
  X_Amz_Signature: Scalars['String']['output'];
  bucket: Scalars['String']['output'];
  key: Scalars['String']['output'];
};

export type Query = {
  __typename?: 'Query';
  accessRequests: PaginatedAccessRequestResponse;
  addressesByUserId: Array<Maybe<Address>>;
  adminAssociationById: AdminAssociation;
  adminAssociations: PaginatedAdminAssociationResponse;
  adminClubById: AdminClub;
  adminClubEventById?: Maybe<AdminClubEvent>;
  adminClubEvents: PaginatedAdminClubEventsResponse;
  adminClubMembers?: Maybe<PaginatedAdminClubMembersResponse>;
  adminClubPostById?: Maybe<AdminClubPost>;
  adminClubPosts?: Maybe<PaginatedAdminClubPostsResponse>;
  adminClubPostsByMember?: Maybe<PaginatedAdminClubPostsResponse>;
  adminClubs?: Maybe<PaginatedAdminClubResponse>;
  adminNotifications: PaginatedAdminNotificationsResponse;
  appVersion: AppVersion;
  associationById: Association;
  associations: PaginatedAssociationResponse;
  associationsByUserId: Array<Association>;
  clubEventById?: Maybe<ClubEvent>;
  clubEvents: PaginatedClubEventsResponse;
  clubMembers?: Maybe<PaginatedClubMembersResponse>;
  clubPostById?: Maybe<ClubPost>;
  clubPosts?: Maybe<PaginatedClubPostsResponse>;
  clubProfile: ClubProfile;
  clubReportById: ClubReport;
  clubReports: PaginatedClubReportsResponse;
  clubRequestById: ClubRequest;
  clubRequests: PaginatedClubRequestsResponse;
  clubTemplateById: ClubTemplate;
  clubTemplates?: Maybe<PaginatedClubTemplateResponse>;
  clubsCategories?: Maybe<PaginatedClubCategoriesResponse>;
  continueSignup: User;
  documentById?: Maybe<DocumentItemUnion>;
  documents: Array<Maybe<DocumentItemUnion>>;
  eventById: AssociationEvent;
  events: PaginatedEventResponse;
  faqById?: Maybe<Faq>;
  faqs?: Maybe<PaginatedFaqResponse>;
  listStaff: Array<User>;
  me: User;
  myAssociations: Array<Association>;
  myClubReports: PaginatedClubReportsResponse;
  myClubRequests: PaginatedClubRequestsResponse;
  myClubs?: Maybe<PaginatedClubTemplateResponse>;
  news: PaginatedNewsResponse;
  newsById: News;
  notifications: PaginatedNotificationsResponse;
  reportCategories: Array<ReportCategory>;
  requestById?: Maybe<Request>;
  requests?: Maybe<PaginatedRequestResponse>;
  screeningKeywordById?: Maybe<ScreeningKeyword>;
  screeningKeywords?: Maybe<PaginatedScreeningKeywordsResponse>;
  transactionAccount?: Maybe<TransactionAccount>;
  transactionHistories: PaginatedTransactionHistoryResponse;
  transactionHistoryById?: Maybe<TransactionHistory>;
  units: PaginatedUnitResponse;
  unreadNotificationsCount: Scalars['Int']['output'];
  uploadFileById?: Maybe<UploadFile>;
  uploadFiles: Array<UploadFile>;
  userById: User;
  userSettings: UserSettings;
  users: PaginatedUserResponse;
  usersCreateEvents?: Maybe<PaginatedUsersCreateEventsResponse>;
  usersCreatePosts?: Maybe<PaginatedUsersCreatePostResponse>;
  usersRequestedClubCreation?: Maybe<PaginatedUsersRequestedClubCreationResponse>;
};

export type QueryAccessRequestsArgs = {
  filter?: InputMaybe<AccessRequestFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAddressesByUserIdArgs = {
  addressesByUserIdInput?: InputMaybe<AddressesByUserIdInput>;
};

export type QueryAdminAssociationByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryAdminAssociationsArgs = {
  filter?: InputMaybe<AdminAssociationsFilterInput>;
  orderBy?: InputMaybe<AdminAssociationsOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAdminClubByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryAdminClubEventByIdArgs = {
  clubEventId: Scalars['ID']['input'];
};

export type QueryAdminClubEventsArgs = {
  clubId: Scalars['ID']['input'];
  filter?: InputMaybe<AdminClubEventsFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAdminClubMembersArgs = {
  clubId: Scalars['ID']['input'];
  filter?: InputMaybe<ClubMembersFilterInput>;
  orderBy?: InputMaybe<ClubMembersOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAdminClubPostByIdArgs = {
  postId: Scalars['ID']['input'];
};

export type QueryAdminClubPostsArgs = {
  clubId: Scalars['ID']['input'];
  filter?: InputMaybe<AdminClubPostsFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAdminClubPostsByMemberArgs = {
  clubId: Scalars['ID']['input'];
  clubProfileId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAdminClubsArgs = {
  associationId: Scalars['ID']['input'];
  filter?: InputMaybe<ClubFilterInput>;
  orderBy?: InputMaybe<ClubOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAdminNotificationsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryAssociationByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryAssociationsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type QueryAssociationsByUserIdArgs = {
  input: AssociationsByUserIdInput;
};

export type QueryClubEventByIdArgs = {
  clubEventId: Scalars['ID']['input'];
};

export type QueryClubEventsArgs = {
  clubTemplateId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryClubMembersArgs = {
  clubTemplateId: Scalars['ID']['input'];
  filter?: InputMaybe<ClubMembersFilterInput>;
  orderBy?: InputMaybe<ClubMembersOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryClubPostByIdArgs = {
  postId: Scalars['ID']['input'];
};

export type QueryClubPostsArgs = {
  clubId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryClubReportByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryClubReportsArgs = {
  filter?: InputMaybe<ClubReportsFilterInput>;
  orderBy?: InputMaybe<ClubReportsOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
  userId?: InputMaybe<Scalars['ID']['input']>;
};

export type QueryClubRequestByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryClubRequestsArgs = {
  filter?: InputMaybe<ClubRequestFilterInput>;
  orderBy?: InputMaybe<ClubRequestOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryClubTemplateByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryClubTemplatesArgs = {
  filter?: InputMaybe<ClubTemplatesFilterInput>;
  orderBy?: InputMaybe<ClubOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryClubsCategoriesArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryContinueSignupArgs = {
  input: ContinueSignupInput;
};

export type QueryDocumentByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryDocumentsArgs = {
  input?: InputMaybe<DocumentsInput>;
};

export type QueryEventByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryEventsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryFaqByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryFaqsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
  type?: InputMaybe<FaqTypeEnum>;
};

export type QueryMyAssociationsArgs = {
  input?: InputMaybe<MyAssociationsInput>;
};

export type QueryMyClubReportsArgs = {
  filter?: InputMaybe<ClubReportsFilterInput>;
  orderBy?: InputMaybe<ClubReportsOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryMyClubRequestsArgs = {
  filter?: InputMaybe<ClubRequestFilterInput>;
  orderBy?: InputMaybe<ClubRequestOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryMyClubsArgs = {
  filter?: InputMaybe<ClubFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryNewsArgs = {
  orderBy?: InputMaybe<NewsOrderInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type QueryNewsByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryNotificationsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryRequestByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryRequestsArgs = {
  filter?: InputMaybe<RequestFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryScreeningKeywordByIdArgs = {
  keywordId: Scalars['ID']['input'];
};

export type QueryScreeningKeywordsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryTransactionHistoriesArgs = {
  filter?: InputMaybe<TransactionHistoryFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryTransactionHistoryByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryUnitsArgs = {
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryUploadFileByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryUserByIdArgs = {
  id: Scalars['ID']['input'];
};

export type QueryUsersArgs = {
  filter?: InputMaybe<UserFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
  search?: InputMaybe<Scalars['String']['input']>;
  sort?: InputMaybe<UserSortInput>;
};

export type QueryUsersCreateEventsArgs = {
  clubId: Scalars['ID']['input'];
  filter?: InputMaybe<UsersCreateEventsFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryUsersCreatePostsArgs = {
  clubId: Scalars['ID']['input'];
  filter?: InputMaybe<UsersCreatePostFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type QueryUsersRequestedClubCreationArgs = {
  filter?: InputMaybe<UsersRequestedClubCreationFilterInput>;
  paginationArgs?: InputMaybe<PaginationArgs>;
};

export type RefreshTokenInput = {
  refreshToken: Scalars['String']['input'];
};

export type RegisterCheckAddressInput = {
  address: Scalars['String']['input'];
  city: Scalars['String']['input'];
  state: Scalars['String']['input'];
  userId: Scalars['String']['input'];
  zipCode: Scalars['String']['input'];
};

export type RegisterCheckEmailInput = {
  email: Scalars['String']['input'];
};

export type RegisterCheckEmailInputResponse = {
  __typename?: 'RegisterCheckEmailInputResponse';
  userId: Scalars['String']['output'];
};

export type RegisterCheckInviteCodeInput = {
  code: Scalars['String']['input'];
};

export type RegisterDeviceInput = {
  deviceType?: InputMaybe<Scalars['String']['input']>;
  token: Scalars['String']['input'];
};

export type RegisterInput = {
  associationId?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  firstName: Scalars['String']['input'];
  lastName: Scalars['String']['input'];
  password?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  role: UserRole;
};

export type RemoveAddressInput = {
  id: Scalars['ID']['input'];
};

export type RemoveClubMemberInput = {
  clubTemplateId: Scalars['ID']['input'];
  membershipId: Scalars['ID']['input'];
};

export type ReportCategory = {
  __typename?: 'ReportCategory';
  code: Scalars['String']['output'];
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  ordering: Scalars['Int']['output'];
  title: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export enum ReportStatusEnum {
  Closed = 'CLOSED',
  Open = 'OPEN',
  Rejected = 'REJECTED',
}

export type Request = {
  __typename?: 'Request';
  address?: Maybe<Scalars['String']['output']>;
  attachments?: Maybe<Array<UploadFile>>;
  category: RequestCategory;
  completionDate?: Maybe<Scalars['Date']['output']>;
  contractorName?: Maybe<Scalars['String']['output']>;
  contractorPhone?: Maybe<Scalars['String']['output']>;
  coordinates?: Maybe<Array<Maybe<Scalars['Float']['output']>>>;
  denialReason?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  projectName?: Maybe<Scalars['String']['output']>;
  response?: Maybe<Scalars['String']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
  status: RequestStatus;
  type?: Maybe<Scalars['String']['output']>;
};

export enum RequestCategory {
  Architectural = 'ARCHITECTURAL',
  General = 'GENERAL',
  WorkOrder = 'WORK_ORDER',
}

export type RequestEmailChangeInput = {
  email: Scalars['String']['input'];
};

export type RequestFilterInput = {
  category?: InputMaybe<RequestCategory>;
  isArchived?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<RequestStatus>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export enum RequestStatus {
  Approved = 'APPROVED',
  Closed = 'CLOSED',
  Complete = 'COMPLETE',
  ContingentlyApproved = 'CONTINGENTLY_APPROVED',
  Denied = 'DENIED',
  New = 'NEW',
  Open = 'OPEN',
  Rejected = 'REJECTED',
  Submitted = 'SUBMITTED',
  UnderReview = 'UNDER_REVIEW',
  VendorDispatched = 'VENDOR_DISPATCHED',
}

export type RequestTransactionHistoryExportInput = {
  filter?: InputMaybe<TransactionHistoryFilterInput>;
};

export type ResetPasswordInput = {
  email: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
  otp: Scalars['String']['input'];
};

export type ResubscribeInput = {
  token: Scalars['String']['input'];
};

export type ScreeningKeyword = {
  __typename?: 'ScreeningKeyword';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  keyword: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type SendInvitationsInput = {
  contactIds: Array<Scalars['String']['input']>;
};

export type SendSignupEmailsInput = {
  userIds: Array<Scalars['String']['input']>;
};

export type SetActiveAddressInput = {
  addressId: Scalars['ID']['input'];
  userId: Scalars['ID']['input'];
};

export type SetAdminNotificationActionInput = {
  action: AdminReportActionType;
  id: Scalars['String']['input'];
};

export type SetPrimaryUnitInput = {
  id: Scalars['ID']['input'];
  rented?: InputMaybe<Scalars['Boolean']['input']>;
  tenantEmail?: InputMaybe<Scalars['String']['input']>;
  tenantName?: InputMaybe<Scalars['String']['input']>;
  tenantPhone?: InputMaybe<Scalars['String']['input']>;
};

export type ToggleEventAttendanceInput = {
  eventId: Scalars['ID']['input'];
};

export type ToggleNewsReactionInput = {
  newsId: Scalars['ID']['input'];
};

export type ToggleSameAsUnitAddressInput = {
  addressId: Scalars['ID']['input'];
  userId: Scalars['ID']['input'];
};

export type TransactionAccount = {
  __typename?: 'TransactionAccount';
  accountId: Scalars['ID']['output'];
  outstandingBalance: Scalars['Float']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type TransactionHistory = {
  __typename?: 'TransactionHistory';
  amount: Scalars['Float']['output'];
  chgDescription?: Maybe<Scalars['String']['output']>;
  created: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  ledgerDate?: Maybe<Scalars['DateTime']['output']>;
  type: Scalars['String']['output'];
};

export type TransactionHistoryExportResponse = {
  __typename?: 'TransactionHistoryExportResponse';
  success: Scalars['Boolean']['output'];
};

export type TransactionHistoryFilterInput = {
  endDate?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UnbanClubMemberInput = {
  clubTemplateId: Scalars['ID']['input'];
  membershipId: Scalars['ID']['input'];
};

export type Unit = {
  __typename?: 'Unit';
  address?: Maybe<Address>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  rented: Scalars['Boolean']['output'];
  tenantEmail?: Maybe<Scalars['String']['output']>;
  tenantName?: Maybe<Scalars['String']['output']>;
  tenantPhone?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type UnsubscribeInput = {
  token: Scalars['String']['input'];
};

export type UpdateAddressInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  state?: InputMaybe<Scalars['String']['input']>;
  street?: InputMaybe<Scalars['String']['input']>;
  zipCode?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAppVersionInput = {
  currentVersion: Scalars['String']['input'];
  minSupportedVersion: Scalars['String']['input'];
};

export type UpdateClubEventInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  endTime: Scalars['DateTime']['input'];
  id: Scalars['ID']['input'];
  location: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  startTime: Scalars['DateTime']['input'];
};

export type UpdateClubProfileInput = {
  displayName?: InputMaybe<Scalars['String']['input']>;
  imgId?: InputMaybe<Scalars['ID']['input']>;
};

export type UpdateClubRequestInput = {
  category?: InputMaybe<ClubCategoryEnum>;
  clubAbout?: InputMaybe<Scalars['String']['input']>;
  clubDescription?: InputMaybe<Scalars['String']['input']>;
  clubName?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  lastName?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<ClubRequestStatus>;
};

export type UpdateClubTemplateInput = {
  about?: InputMaybe<Scalars['String']['input']>;
  category?: InputMaybe<ClubCategoryEnum>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  imgId?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePasswordInput = {
  currentPassword: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
};

export type UpdateProfileInput = {
  birthdayDay?: InputMaybe<Scalars['Int']['input']>;
  birthdayMonth?: InputMaybe<BirthdayMonth>;
  dob?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateReportCategoryInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  ordering?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserSettingsInput = {
  allowNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  clubEventNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  clubNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  clubPostNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  emailCommunications?: InputMaybe<Scalars['Boolean']['input']>;
  eventNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  newsNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  publicRoster?: InputMaybe<Scalars['Boolean']['input']>;
  requestNotifications?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UploadFile = {
  __typename?: 'UploadFile';
  createdAt: Scalars['DateTime']['output'];
  filename: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  key: Scalars['String']['output'];
  mimeType: Scalars['String']['output'];
  size?: Maybe<Scalars['Int']['output']>;
  status: UploadFileStatus;
  updatedAt: Scalars['DateTime']['output'];
  url?: Maybe<Scalars['String']['output']>;
};

export enum UploadFileStatus {
  Deleted = 'DELETED',
  New = 'NEW',
  Used = 'USED',
}

export type UploadUrlResponse = {
  __typename?: 'UploadUrlResponse';
  presignedUrl: PresignedUrl;
  uploadFile: UploadFile;
};

export type User = {
  __typename?: 'User';
  addresses?: Maybe<Array<Maybe<Address>>>;
  association?: Maybe<Association>;
  birthdayDay?: Maybe<Scalars['Int']['output']>;
  birthdayMonth?: Maybe<BirthdayMonth>;
  canUseClubs?: Maybe<Scalars['Boolean']['output']>;
  contact?: Maybe<Contact>;
  dateJoined?: Maybe<Scalars['Date']['output']>;
  dob?: Maybe<Scalars['Date']['output']>;
  email: Scalars['String']['output'];
  firstName: Scalars['String']['output'];
  hasSeenComposeDisclaimer?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  isDeleted?: Maybe<Scalars['Boolean']['output']>;
  lastName: Scalars['String']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  role?: Maybe<UserRole>;
};

export type UserFilterInput = {
  associationId?: InputMaybe<Scalars['ID']['input']>;
  dateJoinedFrom?: InputMaybe<Scalars['Date']['input']>;
  dateJoinedTo?: InputMaybe<Scalars['Date']['input']>;
  showDeleted?: InputMaybe<Scalars['Boolean']['input']>;
};

export enum UserRole {
  Admin = 'ADMIN',
  Editor = 'EDITOR',
  User = 'USER',
}

export type UserSettings = {
  __typename?: 'UserSettings';
  allowNotifications: Scalars['Boolean']['output'];
  clubEventNotifications: Scalars['Boolean']['output'];
  clubNotifications: Scalars['Boolean']['output'];
  clubPostNotifications: Scalars['Boolean']['output'];
  emailCommunications: Scalars['Boolean']['output'];
  eventNotifications: Scalars['Boolean']['output'];
  newsNotifications: Scalars['Boolean']['output'];
  requestNotifications: Scalars['Boolean']['output'];
};

export enum UserSortField {
  DateJoined = 'DATE_JOINED',
  Id = 'ID',
  SalesforceId = 'SALESFORCE_ID',
}

export type UserSortInput = {
  direction?: InputMaybe<OrderDirection>;
  field: UserSortField;
};

export type UsersCreateEventsFilterInput = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export type UsersCreatePostFilterInput = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export type UsersRequestedClubCreationFilterInput = {
  search?: InputMaybe<Scalars['String']['input']>;
};

export enum VantacaTransactionTypeEnum {
  Adjustment = 'ADJUSTMENT',
  Charge = 'CHARGE',
  CreditDistribution = 'CREDIT_DISTRIBUTION',
  Payment = 'PAYMENT',
  PaymentAdjustment = 'PAYMENT_ADJUSTMENT',
  RefundAch = 'REFUND_ACH',
  RefundCheck = 'REFUND_CHECK',
  Void = 'VOID',
  Writeoff = 'WRITEOFF',
}

export type VerifyEmailChangeInput = {
  email: Scalars['String']['input'];
  otp: Scalars['String']['input'];
};

export type AdminAssociationsQueryVariables = Exact<{
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<AdminAssociationsFilterInput>;
  orderBy?: InputMaybe<AdminAssociationsOrderInput>;
}>;

export type AdminAssociationsQuery = {
  __typename?: 'Query';
  adminAssociations: {
    __typename?: 'PaginatedAdminAssociationResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'AdminAssociation';
      activeClubCount?: number | null;
      canUseClubs?: boolean | null;
      clubMemberCount?: number | null;
      createdAt: any;
      id: string;
      memberCount?: number | null;
      name: string;
      updatedAt: any;
    }>;
  };
};

export type AdminAssociationByIdQueryVariables = Exact<{
  adminAssociationByIdId: Scalars['ID']['input'];
}>;

export type AdminAssociationByIdQuery = {
  __typename?: 'Query';
  adminAssociationById: {
    __typename?: 'AdminAssociation';
    clubMemberCount?: number | null;
    id: string;
    name: string;
    memberCount?: number | null;
    createdAt: any;
    updatedAt: any;
  };
};

export type ToggleAssociationClubFeatureMutationVariables = Exact<{
  associationId: Scalars['ID']['input'];
}>;

export type ToggleAssociationClubFeatureMutation = {
  __typename?: 'Mutation';
  toggleAssociationClubFeature: {
    __typename?: 'Association';
    id: string;
    name: string;
    canUseClubs?: boolean | null;
  };
};

export type MeQueryVariables = Exact<{ [key: string]: never }>;

export type MeQuery = {
  __typename?: 'Query';
  me: {
    __typename?: 'User';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role?: UserRole | null;
  };
};

export type RegisterMutationVariables = Exact<{
  input: RegisterInput;
}>;

export type RegisterMutation = {
  __typename?: 'Mutation';
  register: { __typename?: 'Message'; message: string };
};

export type LoginMutationVariables = Exact<{
  input: LoginInput;
}>;

export type LoginMutation = {
  __typename?: 'Mutation';
  login: {
    __typename?: 'AuthToken';
    accessToken: string;
    refreshToken: string;
    accessTokenExpireTime: number;
    refreshTokenExpireTime: number;
    role?: string | null;
  };
};

export type RefreshTokenMutationVariables = Exact<{
  input: RefreshTokenInput;
}>;

export type RefreshTokenMutation = {
  __typename?: 'Mutation';
  refreshToken: {
    __typename?: 'AuthToken';
    accessToken: string;
    refreshToken: string;
    accessTokenExpireTime: number;
    refreshTokenExpireTime: number;
  };
};

export type ForgotPasswordMutationVariables = Exact<{
  input: ForgotPasswordInput;
}>;

export type ForgotPasswordMutation = {
  __typename?: 'Mutation';
  forgotPassword: { __typename?: 'Message'; message: string };
};

export type ResetPasswordMutationVariables = Exact<{
  input: ResetPasswordInput;
}>;

export type ResetPasswordMutation = {
  __typename?: 'Mutation';
  resetPassword: { __typename?: 'Message'; message: string };
};

export type CompleteSignupMutationVariables = Exact<{
  input: CompleteSignupInput;
}>;

export type CompleteSignupMutation = {
  __typename?: 'Mutation';
  completeSignup: {
    __typename?: 'AuthToken';
    accessToken: string;
    refreshToken: string;
    accessTokenExpireTime: number;
    refreshTokenExpireTime: number;
  };
};

export type AdminClubDetailByIdQueryVariables = Exact<{
  adminClubByIdId: Scalars['ID']['input'];
}>;

export type AdminClubDetailByIdQuery = {
  __typename?: 'Query';
  adminClubById: {
    __typename?: 'AdminClub';
    id: string;
    activatedAt?: any | null;
    memberCount?: number | null;
    clubTemplate?: {
      __typename?: 'ClubTemplate';
      id: string;
      name: string;
      description?: string | null;
      about?: string | null;
      category?: ClubCategoryEnum | null;
      newPost?: boolean | null;
      hasJoined?: boolean | null;
      memberCount?: number | null;
      img?: {
        __typename?: 'UploadFile';
        id: string;
        filename: string;
        key: string;
        mimeType: string;
        size?: number | null;
        status: UploadFileStatus;
        createdAt: any;
        updatedAt: any;
        url?: string | null;
      } | null;
    } | null;
  };
};

export type ClubMembersQueryVariables = Exact<{
  clubTemplateId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<ClubMembersFilterInput>;
  orderBy?: InputMaybe<ClubMembersOrderInput>;
}>;

export type ClubMembersQuery = {
  __typename?: 'Query';
  clubMembers?: {
    __typename?: 'PaginatedClubMembersResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'ClubMembership';
      id: string;
      status: MembershipStatusEnum;
      joinedAt: any;
      deletedAt?: any | null;
      clubProfile: {
        __typename?: 'ClubProfile';
        id: string;
        displayName: string;
        img?: {
          __typename?: 'UploadFile';
          id: string;
          filename: string;
          key: string;
          mimeType: string;
          size?: number | null;
          status: UploadFileStatus;
          createdAt: any;
          updatedAt: any;
          url?: string | null;
        } | null;
      };
    }>;
  } | null;
};

export type AdminClubPostsQueryVariables = Exact<{
  clubId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<AdminClubPostsFilterInput>;
}>;

export type AdminClubPostsQuery = {
  __typename?: 'Query';
  adminClubPosts?: {
    __typename?: 'PaginatedAdminClubPostsResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'AdminClubPost';
      id: string;
      clubId: string;
      content?: string | null;
      isPinned?: boolean | null;
      reactionCount?: number | null;
      createdAt?: any | null;
      updatedAt?: any | null;
      deletedAt?: any | null;
      clubProfile: {
        __typename?: 'AdminClubProfile';
        id: string;
        displayName: string;
        createdAt: any;
        updatedAt: any;
        img?: {
          __typename?: 'UploadFile';
          id: string;
          filename: string;
          key: string;
          mimeType: string;
          size?: number | null;
          status: UploadFileStatus;
          createdAt: any;
          updatedAt: any;
          url?: string | null;
        } | null;
        user?: {
          __typename?: 'User';
          id: string;
          firstName: string;
          lastName: string;
          email: string;
          role?: UserRole | null;
          canUseClubs?: boolean | null;
        } | null;
      };
      reactions?: Array<{
        __typename?: 'ClubPostReaction';
        id: string;
        postId: string;
        clubProfileId: string;
        createdAt: any;
      }> | null;
      reports?: Array<{
        __typename?: 'ClubReport';
        id: string;
        status: ReportStatusEnum;
        details?: string | null;
        createdAt?: any | null;
        clubPost?: {
          __typename?: 'ClubPost';
          id: string;
          clubId: string;
          content?: string | null;
          isPinned?: boolean | null;
          reactionCount?: number | null;
          createdAt?: any | null;
          updatedAt?: any | null;
          clubProfile: { __typename?: 'ClubProfile'; id: string; displayName: string };
        } | null;
        category?: {
          __typename?: 'ReportCategory';
          id: string;
          code: string;
          title: string;
          description?: string | null;
          ordering: number;
          createdAt?: any | null;
          updatedAt?: any | null;
        } | null;
        reporter: {
          __typename?: 'AdminClubProfile';
          id: string;
          displayName: string;
          createdAt: any;
          updatedAt: any;
        };
      }> | null;
    }>;
  } | null;
};

export type AdminClubPostByIdQueryVariables = Exact<{
  postId: Scalars['ID']['input'];
}>;

export type AdminClubPostByIdQuery = {
  __typename?: 'Query';
  adminClubPostById?: {
    __typename?: 'AdminClubPost';
    id: string;
    clubId: string;
    content?: string | null;
    isPinned?: boolean | null;
    reactionCount?: number | null;
    createdAt?: any | null;
    updatedAt?: any | null;
    deletedAt?: any | null;
    clubProfile: {
      __typename?: 'AdminClubProfile';
      id: string;
      displayName: string;
      createdAt: any;
      updatedAt: any;
      img?: {
        __typename?: 'UploadFile';
        id: string;
        filename: string;
        key: string;
        mimeType: string;
        size?: number | null;
        status: UploadFileStatus;
        createdAt: any;
        updatedAt: any;
        url?: string | null;
      } | null;
      user?: {
        __typename?: 'User';
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phone?: string | null;
        dob?: any | null;
        birthdayMonth?: BirthdayMonth | null;
        birthdayDay?: number | null;
        dateJoined?: any | null;
        role?: UserRole | null;
        isDeleted?: boolean | null;
        addresses?: Array<{
          __typename?: 'Address';
          id: string;
          street?: string | null;
          city?: string | null;
          state?: string | null;
          zipCode?: string | null;
          isActive: boolean;
          isPrimaryUnit: boolean;
          source?: AddressSource | null;
          createdAt?: any | null;
          updatedAt?: any | null;
          isSameAsUnitAddress?: boolean | null;
        } | null> | null;
        association?: { __typename?: 'Association'; id: string; name: string } | null;
        contact?: { __typename?: 'Contact'; id: string; salesforceId: string } | null;
      } | null;
    };
    reactions?: Array<{
      __typename?: 'ClubPostReaction';
      id: string;
      postId: string;
      clubProfileId: string;
      createdAt: any;
    }> | null;
    reports?: Array<{
      __typename?: 'ClubReport';
      id: string;
      status: ReportStatusEnum;
      details?: string | null;
      createdAt?: any | null;
      clubPost?: {
        __typename?: 'ClubPost';
        id: string;
        clubId: string;
        content?: string | null;
        isPinned?: boolean | null;
        reactionCount?: number | null;
        createdAt?: any | null;
        updatedAt?: any | null;
        clubProfile: { __typename?: 'ClubProfile'; id: string; displayName: string };
      } | null;
      category?: {
        __typename?: 'ReportCategory';
        id: string;
        code: string;
        title: string;
        description?: string | null;
        ordering: number;
        createdAt?: any | null;
        updatedAt?: any | null;
      } | null;
      reporter: {
        __typename?: 'AdminClubProfile';
        id: string;
        displayName: string;
        createdAt: any;
        updatedAt: any;
      };
    }> | null;
  } | null;
};

export type AdminClubEventsQueryVariables = Exact<{
  clubId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<AdminClubEventsFilterInput>;
}>;

export type AdminClubEventsQuery = {
  __typename?: 'Query';
  adminClubEvents: {
    __typename?: 'PaginatedAdminClubEventsResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'AdminClubEvent';
      id: string;
      name?: string | null;
      startTime?: any | null;
      endTime?: any | null;
      location?: string | null;
      description?: string | null;
      reactionCount?: number | null;
      createdAt?: any | null;
      updatedAt?: any | null;
      deletedAt?: any | null;
      clubProfile?: {
        __typename?: 'AdminClubProfile';
        id: string;
        displayName: string;
        createdAt: any;
        updatedAt: any;
        img?: { __typename?: 'UploadFile'; url?: string | null } | null;
        user?: {
          __typename?: 'User';
          id: string;
          firstName: string;
          lastName: string;
          email: string;
          phone?: string | null;
          dob?: any | null;
          birthdayMonth?: BirthdayMonth | null;
          birthdayDay?: number | null;
          dateJoined?: any | null;
          role?: UserRole | null;
          isDeleted?: boolean | null;
          canUseClubs?: boolean | null;
        } | null;
      } | null;
      reports?: Array<{
        __typename?: 'ClubReport';
        id: string;
        status: ReportStatusEnum;
        details?: string | null;
        createdAt?: any | null;
        category?: {
          __typename?: 'ReportCategory';
          id: string;
          code: string;
          title: string;
          description?: string | null;
          ordering: number;
          createdAt?: any | null;
          updatedAt?: any | null;
        } | null;
      }> | null;
    }>;
  };
};

export type UsersCreateEventsQueryVariables = Exact<{
  clubId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<UsersCreateEventsFilterInput>;
}>;

export type UsersCreateEventsQuery = {
  __typename?: 'Query';
  usersCreateEvents?: {
    __typename?: 'PaginatedUsersCreateEventsResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'User';
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phone?: string | null;
      dob?: any | null;
      birthdayMonth?: BirthdayMonth | null;
      birthdayDay?: number | null;
      dateJoined?: any | null;
      role?: UserRole | null;
      isDeleted?: boolean | null;
      canUseClubs?: boolean | null;
    }>;
  } | null;
};

export type AdminClubEventByIdQueryVariables = Exact<{
  clubEventId: Scalars['ID']['input'];
}>;

export type AdminClubEventByIdQuery = {
  __typename?: 'Query';
  adminClubEventById?: {
    __typename?: 'AdminClubEvent';
    id: string;
    name?: string | null;
    startTime?: any | null;
    endTime?: any | null;
    location?: string | null;
    description?: string | null;
    reactionCount?: number | null;
    createdAt?: any | null;
    updatedAt?: any | null;
    clubProfile?: {
      __typename?: 'AdminClubProfile';
      id: string;
      displayName: string;
      img?: {
        __typename?: 'UploadFile';
        id: string;
        filename: string;
        key: string;
        mimeType: string;
        size?: number | null;
        status: UploadFileStatus;
        createdAt: any;
        updatedAt: any;
        url?: string | null;
      } | null;
    } | null;
  } | null;
};

export type ClubRequestByIdQueryVariables = Exact<{
  clubRequestByIdId: Scalars['ID']['input'];
}>;

export type ClubRequestByIdQuery = {
  __typename?: 'Query';
  clubRequestById: {
    __typename?: 'ClubRequest';
    id: string;
    clubName: string;
    clubDescription: string;
    category?: ClubCategoryEnum | null;
    clubAbout?: string | null;
    status: ClubRequestStatus;
    createdAt: any;
    clubProfile?: {
      __typename?: 'ClubProfile';
      id: string;
      displayName: string;
      img?: {
        __typename?: 'UploadFile';
        id: string;
        filename: string;
        key: string;
        mimeType: string;
        size?: number | null;
        status: UploadFileStatus;
        createdAt: any;
        updatedAt: any;
        url?: string | null;
      } | null;
    } | null;
  };
};

export type AdminRemoveClubPostByIdMutationVariables = Exact<{
  postId: Scalars['ID']['input'];
}>;

export type AdminRemoveClubPostByIdMutation = {
  __typename?: 'Mutation';
  adminRemoveClubPostById: {
    __typename?: 'ClubPost';
    id: string;
    clubId: string;
    content?: string | null;
    isPinned?: boolean | null;
    reactionCount?: number | null;
    hasReacted?: boolean | null;
    createdAt?: any | null;
    updatedAt?: any | null;
  };
};

export type AdminClubMembersQueryVariables = Exact<{
  clubId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<ClubMembersFilterInput>;
  orderBy?: InputMaybe<ClubMembersOrderInput>;
}>;

export type AdminClubMembersQuery = {
  __typename?: 'Query';
  adminClubMembers?: {
    __typename?: 'PaginatedAdminClubMembersResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'AdminClubMembership';
      id: string;
      status: MembershipStatusEnum;
      joinedAt: any;
      deletedAt?: any | null;
      clubProfile: {
        __typename?: 'AdminClubProfile';
        id: string;
        displayName: string;
        createdAt: any;
        updatedAt: any;
        img?: {
          __typename?: 'UploadFile';
          id: string;
          filename: string;
          key: string;
          mimeType: string;
          size?: number | null;
          status: UploadFileStatus;
          createdAt: any;
          updatedAt: any;
          url?: string | null;
        } | null;
        user?: {
          __typename?: 'User';
          id: string;
          firstName: string;
          lastName: string;
          email: string;
          phone?: string | null;
          dob?: any | null;
          birthdayMonth?: BirthdayMonth | null;
          birthdayDay?: number | null;
          dateJoined?: any | null;
          role?: UserRole | null;
          isDeleted?: boolean | null;
          canUseClubs?: boolean | null;
        } | null;
      };
    }>;
  } | null;
};

export type UsersCreatePostsQueryVariables = Exact<{
  clubId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<UsersCreatePostFilterInput>;
}>;

export type UsersCreatePostsQuery = {
  __typename?: 'Query';
  usersCreatePosts?: {
    __typename?: 'PaginatedUsersCreatePostResponse';
    limit: number;
    page: number;
    total: number;
    items: Array<{
      __typename?: 'User';
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phone?: string | null;
      dob?: any | null;
      birthdayMonth?: BirthdayMonth | null;
      birthdayDay?: number | null;
      dateJoined?: any | null;
      role?: UserRole | null;
      isDeleted?: boolean | null;
      canUseClubs?: boolean | null;
    }>;
  } | null;
};

export type AdminUnflagReportsByPostIdMutationVariables = Exact<{
  postId: Scalars['ID']['input'];
}>;

export type AdminUnflagReportsByPostIdMutation = {
  __typename?: 'Mutation';
  adminUnflagReportsByPostId: {
    __typename?: 'ClubPost';
    clubId: string;
    id: string;
    updatedAt?: any | null;
    createdAt?: any | null;
  };
};

export type AdminDeleteClubEventMutationVariables = Exact<{
  clubEventId: Scalars['ID']['input'];
}>;

export type AdminDeleteClubEventMutation = {
  __typename?: 'Mutation';
  adminDeleteClubEvent: {
    __typename?: 'ClubEvent';
    id: string;
    name?: string | null;
    createdAt?: any | null;
    updatedAt?: any | null;
  };
};

export type AdminUnflagReportsByEventIdMutationVariables = Exact<{
  eventId: Scalars['ID']['input'];
}>;

export type AdminUnflagReportsByEventIdMutation = {
  __typename?: 'Mutation';
  adminUnflagReportsByEventId: {
    __typename?: 'ClubEvent';
    id: string;
    name?: string | null;
    createdAt?: any | null;
    updatedAt?: any | null;
  };
};

export type RemoveClubMemberMutationVariables = Exact<{
  input: RemoveClubMemberInput;
}>;

export type RemoveClubMemberMutation = {
  __typename?: 'Mutation';
  removeClubMember: {
    __typename?: 'ClubActionResponse';
    clubTemplateId: string;
    membershipId?: string | null;
  };
};

export type ClubTemplatesQueryVariables = Exact<{
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<ClubTemplatesFilterInput>;
  orderBy?: InputMaybe<ClubOrderInput>;
}>;

export type ClubTemplatesQuery = {
  __typename?: 'Query';
  clubTemplates?: {
    __typename?: 'PaginatedClubTemplateResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'ClubTemplate';
      id: string;
      name: string;
      description?: string | null;
      about?: string | null;
      category?: ClubCategoryEnum | null;
      newPost?: boolean | null;
      hasJoined?: boolean | null;
      memberCount?: number | null;
      updatedAt?: any | null;
      img?: {
        __typename?: 'UploadFile';
        id: string;
        filename: string;
        key: string;
        mimeType: string;
        size?: number | null;
        status: UploadFileStatus;
        createdAt: any;
        updatedAt: any;
        url?: string | null;
      } | null;
    }>;
  } | null;
};

export type ClubTemplateByIdQueryVariables = Exact<{
  clubTemplateByIdId: Scalars['ID']['input'];
}>;

export type ClubTemplateByIdQuery = {
  __typename?: 'Query';
  clubTemplateById: {
    __typename?: 'ClubTemplate';
    id: string;
    name: string;
    description?: string | null;
    about?: string | null;
    category?: ClubCategoryEnum | null;
    newPost?: boolean | null;
    img?: { __typename?: 'UploadFile'; id: string; filename: string; url?: string | null } | null;
  };
};

export type AdminClubByIdQueryVariables = Exact<{
  adminClubByIdId: Scalars['ID']['input'];
}>;

export type AdminClubByIdQuery = {
  __typename?: 'Query';
  adminClubById: {
    __typename?: 'AdminClub';
    id: string;
    activatedAt?: any | null;
    memberCount?: number | null;
    clubTemplate?: {
      __typename?: 'ClubTemplate';
      id: string;
      name: string;
      description?: string | null;
      about?: string | null;
      category?: ClubCategoryEnum | null;
      newPost?: boolean | null;
      img?: { __typename?: 'UploadFile'; id: string; filename: string; url?: string | null } | null;
    } | null;
  };
};

export type AdminClubsQueryVariables = Exact<{
  associationId: Scalars['ID']['input'];
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<ClubFilterInput>;
  orderBy?: InputMaybe<ClubOrderInput>;
}>;

export type AdminClubsQuery = {
  __typename?: 'Query';
  adminClubs?: {
    __typename?: 'PaginatedAdminClubResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'AdminClub';
      id: string;
      activatedAt?: any | null;
      lastActivity?: any | null;
      memberCount?: number | null;
      clubTemplate?: {
        __typename?: 'ClubTemplate';
        id: string;
        name: string;
        description?: string | null;
        about?: string | null;
        category?: ClubCategoryEnum | null;
        newPost?: boolean | null;
        img?: {
          __typename?: 'UploadFile';
          id: string;
          filename: string;
          url?: string | null;
        } | null;
      } | null;
    }>;
  } | null;
};

export type CreateClubTemplateMutationVariables = Exact<{
  input: CreateClubTemplateInput;
}>;

export type CreateClubTemplateMutation = {
  __typename?: 'Mutation';
  createClubTemplate: {
    __typename?: 'ClubTemplate';
    id: string;
    name: string;
    description?: string | null;
    about?: string | null;
    category?: ClubCategoryEnum | null;
    newPost?: boolean | null;
    hasJoined?: boolean | null;
    memberCount?: number | null;
    img?: {
      __typename?: 'UploadFile';
      id: string;
      filename: string;
      key: string;
      mimeType: string;
      size?: number | null;
      status: UploadFileStatus;
      createdAt: any;
      updatedAt: any;
      url?: string | null;
    } | null;
  };
};

export type UpdateClubTemplateMutationVariables = Exact<{
  input: UpdateClubTemplateInput;
}>;

export type UpdateClubTemplateMutation = {
  __typename?: 'Mutation';
  updateClubTemplate: {
    __typename?: 'ClubTemplate';
    id: string;
    name: string;
    description?: string | null;
    about?: string | null;
    category?: ClubCategoryEnum | null;
    newPost?: boolean | null;
    img?: { __typename?: 'UploadFile'; id: string; filename: string; url?: string | null } | null;
  };
};

export type DeleteClubTemplateMutationVariables = Exact<{
  deleteClubTemplateId: Scalars['ID']['input'];
}>;

export type DeleteClubTemplateMutation = { __typename?: 'Mutation'; deleteClubTemplate: boolean };

export type CreateUploadUrlMutationVariables = Exact<{
  input: CreateUploadFileInput;
}>;

export type CreateUploadUrlMutation = {
  __typename?: 'Mutation';
  createUploadUrl: {
    __typename?: 'UploadUrlResponse';
    uploadFile: {
      __typename?: 'UploadFile';
      id: string;
      filename: string;
      key: string;
      mimeType: string;
      size?: number | null;
      status: UploadFileStatus;
      createdAt: any;
      updatedAt: any;
      url?: string | null;
    };
    presignedUrl: {
      __typename?: 'PresignedUrl';
      url: string;
      fields: {
        __typename?: 'PresignedUrlFields';
        bucket: string;
        key: string;
        Policy: string;
        X_Amz_Algorithm: string;
        X_Amz_Credential: string;
        X_Amz_Date: string;
        X_Amz_Signature: string;
      };
    };
  };
};

export type ClubRequestsQueryVariables = Exact<{
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<ClubRequestFilterInput>;
  orderBy?: InputMaybe<ClubRequestOrderInput>;
}>;

export type ClubRequestsQuery = {
  __typename?: 'Query';
  clubRequests: {
    __typename?: 'PaginatedClubRequestsResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'ClubRequest';
      category?: ClubCategoryEnum | null;
      clubAbout?: string | null;
      clubDescription: string;
      clubName: string;
      createdAt: any;
      id: string;
      status: ClubRequestStatus;
      clubProfile?: {
        __typename?: 'ClubProfile';
        id: string;
        displayName: string;
        img?: {
          __typename?: 'UploadFile';
          id: string;
          filename: string;
          key: string;
          mimeType: string;
          size?: number | null;
          status: UploadFileStatus;
          createdAt: any;
          updatedAt: any;
          url?: string | null;
        } | null;
      } | null;
      user?: {
        __typename?: 'User';
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        phone?: string | null;
        dob?: any | null;
        birthdayMonth?: BirthdayMonth | null;
        birthdayDay?: number | null;
        dateJoined?: any | null;
        role?: UserRole | null;
        isDeleted?: boolean | null;
        canUseClubs?: boolean | null;
      } | null;
    }>;
  };
};

export type UpdateClubRequestMutationVariables = Exact<{
  input: UpdateClubRequestInput;
}>;

export type UpdateClubRequestMutation = {
  __typename?: 'Mutation';
  updateClubRequest: {
    __typename?: 'ClubRequest';
    id: string;
    clubName: string;
    clubDescription: string;
    category?: ClubCategoryEnum | null;
    clubAbout?: string | null;
    status: ClubRequestStatus;
    createdAt: any;
    clubProfile?: { __typename?: 'ClubProfile'; id: string; displayName: string } | null;
  };
};

export type UsersRequestedClubCreationQueryVariables = Exact<{
  paginationArgs?: InputMaybe<PaginationArgs>;
  filter?: InputMaybe<UsersRequestedClubCreationFilterInput>;
}>;

export type UsersRequestedClubCreationQuery = {
  __typename?: 'Query';
  usersRequestedClubCreation?: {
    __typename?: 'PaginatedUsersRequestedClubCreationResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'User';
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phone?: string | null;
      dob?: any | null;
      birthdayMonth?: BirthdayMonth | null;
      birthdayDay?: number | null;
      dateJoined?: any | null;
      role?: UserRole | null;
      isDeleted?: boolean | null;
      canUseClubs?: boolean | null;
    }>;
  } | null;
};

export type ApprovedClubRequestCreationMutationVariables = Exact<{
  clubRequestId: Scalars['ID']['input'];
  clubTemplateId: Scalars['ID']['input'];
}>;

export type ApprovedClubRequestCreationMutation = {
  __typename?: 'Mutation';
  approvedClubRequestCreation: {
    __typename?: 'ClubRequest';
    id: string;
    clubName: string;
    clubDescription: string;
    category?: ClubCategoryEnum | null;
    clubAbout?: string | null;
    status: ClubRequestStatus;
    createdAt: any;
  };
};

export type RejectedClubRequestCreationMutationVariables = Exact<{
  clubRequestId: Scalars['ID']['input'];
}>;

export type RejectedClubRequestCreationMutation = {
  __typename?: 'Mutation';
  rejectedClubRequestCreation: {
    __typename?: 'ClubRequest';
    id: string;
    clubName: string;
    clubDescription: string;
    category?: ClubCategoryEnum | null;
    clubAbout?: string | null;
    status: ClubRequestStatus;
    createdAt: any;
  };
};

export type AssociationsByUserIdQueryVariables = Exact<{
  input: AssociationsByUserIdInput;
}>;

export type AssociationsByUserIdQuery = {
  __typename?: 'Query';
  associationsByUserId: Array<{ __typename?: 'Association'; id: string; name: string }>;
};

export type AdminUpdateProfileMutationVariables = Exact<{
  input: AdminUpdateProfileInput;
}>;

export type AdminUpdateProfileMutation = {
  __typename?: 'Mutation';
  adminUpdateProfile: {
    __typename?: 'User';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role?: UserRole | null;
    association?: { __typename?: 'Association'; id: string; name: string } | null;
  };
};

export type AdminNotificationsQueryVariables = Exact<{
  paginationArgs?: InputMaybe<PaginationArgs>;
}>;

export type AdminNotificationsQuery = {
  __typename?: 'Query';
  adminNotifications: {
    __typename?: 'PaginatedAdminNotificationsResponse';
    limit: number;
    page: number;
    total: number;
    items: Array<{
      __typename?: 'AdminNotification';
      id: string;
      type: AdminNotificationType;
      isRead: boolean;
      createdAt: any;
      updatedAt: any;
      reportedAt: any;
      action?: AdminReportActionType | null;
      payload?: {
        __typename?: 'AdminNotificationPayload';
        postId?: string | null;
        eventId?: string | null;
      } | null;
      clubPost?: {
        __typename?: 'AdminClubPost';
        id: string;
        clubId: string;
        content?: string | null;
        isPinned?: boolean | null;
        reactionCount?: number | null;
        createdAt?: any | null;
        updatedAt?: any | null;
        deletedAt?: any | null;
        clubProfile: {
          __typename?: 'AdminClubProfile';
          id: string;
          displayName: string;
          createdAt: any;
          updatedAt: any;
          img?: {
            __typename?: 'UploadFile';
            id: string;
            filename: string;
            key: string;
            mimeType: string;
            size?: number | null;
            status: UploadFileStatus;
            createdAt: any;
            updatedAt: any;
            url?: string | null;
          } | null;
          user?: {
            __typename?: 'User';
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            phone?: string | null;
            dob?: any | null;
            birthdayMonth?: BirthdayMonth | null;
            birthdayDay?: number | null;
            dateJoined?: any | null;
            role?: UserRole | null;
            isDeleted?: boolean | null;
            canUseClubs?: boolean | null;
            addresses?: Array<{
              __typename?: 'Address';
              id: string;
              street?: string | null;
              city?: string | null;
              state?: string | null;
              zipCode?: string | null;
              isActive: boolean;
              isPrimaryUnit: boolean;
              source?: AddressSource | null;
              createdAt?: any | null;
              updatedAt?: any | null;
              isSameAsUnitAddress?: boolean | null;
            } | null> | null;
            association?: {
              __typename?: 'Association';
              id: string;
              name: string;
              canUseClubs?: boolean | null;
            } | null;
            contact?: { __typename?: 'Contact'; id: string; salesforceId: string } | null;
          } | null;
        };
        reactions?: Array<{
          __typename?: 'ClubPostReaction';
          id: string;
          postId: string;
          clubProfileId: string;
          createdAt: any;
        }> | null;
        reports?: Array<{
          __typename?: 'ClubReport';
          id: string;
          status: ReportStatusEnum;
          details?: string | null;
          createdAt?: any | null;
          clubPost?: {
            __typename?: 'ClubPost';
            id: string;
            clubId: string;
            content?: string | null;
            isPinned?: boolean | null;
            isNew?: boolean | null;
            reactionCount?: number | null;
            hasReacted?: boolean | null;
            createdAt?: any | null;
            updatedAt?: any | null;
            clubProfile: { __typename?: 'ClubProfile'; id: string; displayName: string };
          } | null;
          clubEvent?: {
            __typename?: 'ClubEvent';
            id: string;
            name?: string | null;
            startTime?: any | null;
            endTime?: any | null;
            location?: string | null;
            description?: string | null;
            reactionCount?: number | null;
            isNew?: boolean | null;
            hasReacted?: boolean | null;
            createdAt?: any | null;
            updatedAt?: any | null;
          } | null;
          category?: {
            __typename?: 'ReportCategory';
            id: string;
            code: string;
            title: string;
            description?: string | null;
            ordering: number;
            createdAt?: any | null;
            updatedAt?: any | null;
          } | null;
          reporter: {
            __typename?: 'AdminClubProfile';
            id: string;
            displayName: string;
            createdAt: any;
            updatedAt: any;
          };
        }> | null;
      } | null;
      clubEvent?: {
        __typename?: 'AdminClubEvent';
        clubId?: string | null;
        id: string;
        name?: string | null;
        startTime?: any | null;
        endTime?: any | null;
        location?: string | null;
        description?: string | null;
        reactionCount?: number | null;
        createdAt?: any | null;
        updatedAt?: any | null;
        clubProfile?: {
          __typename?: 'AdminClubProfile';
          id: string;
          displayName: string;
          createdAt: any;
          updatedAt: any;
          img?: { __typename?: 'UploadFile'; url?: string | null } | null;
          user?: {
            __typename?: 'User';
            association?: {
              __typename?: 'Association';
              id: string;
              name: string;
              canUseClubs?: boolean | null;
            } | null;
          } | null;
        } | null;
        reports?: Array<{
          __typename?: 'ClubReport';
          id: string;
          status: ReportStatusEnum;
          details?: string | null;
          createdAt?: any | null;
          category?: {
            __typename?: 'ReportCategory';
            id: string;
            code: string;
            title: string;
            description?: string | null;
          } | null;
        }> | null;
      } | null;
      clubRequest?: {
        __typename?: 'ClubRequest';
        id: string;
        clubName: string;
        clubDescription: string;
        category?: ClubCategoryEnum | null;
        clubAbout?: string | null;
        status: ClubRequestStatus;
        createdAt: any;
        clubProfile?: {
          __typename?: 'ClubProfile';
          id: string;
          displayName: string;
          img?: { __typename?: 'UploadFile'; url?: string | null } | null;
        } | null;
        user?: { __typename?: 'User'; firstName: string; id: string; lastName: string } | null;
      } | null;
    }>;
  };
};

export type MarkAdminNotificationsAsReadMutationVariables = Exact<{
  input: MarkNotificationsAsReadInput;
}>;

export type MarkAdminNotificationsAsReadMutation = {
  __typename?: 'Mutation';
  markAdminNotificationsAsRead: boolean;
};

export type ListStaffQueryVariables = Exact<{ [key: string]: never }>;

export type ListStaffQuery = {
  __typename?: 'Query';
  listStaff: Array<{
    __typename?: 'User';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string | null;
    dob?: any | null;
    dateJoined?: any | null;
    role?: UserRole | null;
    addresses?: Array<{
      __typename?: 'Address';
      id: string;
      street?: string | null;
      city?: string | null;
      state?: string | null;
      zipCode?: string | null;
      isActive: boolean;
      source?: AddressSource | null;
      createdAt?: any | null;
      updatedAt?: any | null;
    } | null> | null;
    association?: { __typename?: 'Association'; id: string; name: string } | null;
  }>;
};

export type UpdateProfileMutationVariables = Exact<{
  input: UpdateProfileInput;
}>;

export type UpdateProfileMutation = {
  __typename?: 'Mutation';
  updateProfile: {
    __typename?: 'User';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
};

export type UpdatePasswordMutationVariables = Exact<{
  input: UpdatePasswordInput;
}>;

export type UpdatePasswordMutation = {
  __typename?: 'Mutation';
  updatePassword: { __typename?: 'Message'; message: string };
};

export type UnsubscribeEmailMutationVariables = Exact<{
  input: UnsubscribeInput;
}>;

export type UnsubscribeEmailMutation = {
  __typename?: 'Mutation';
  unsubscribeEmail: { __typename?: 'Message'; message: string };
};

export type UsersQueryVariables = Exact<{
  paginationArgs: PaginationArgs;
  sort?: InputMaybe<UserSortInput>;
  filter?: InputMaybe<UserFilterInput>;
  search?: InputMaybe<Scalars['String']['input']>;
}>;

export type UsersQuery = {
  __typename?: 'Query';
  users: {
    __typename?: 'PaginatedUserResponse';
    total: number;
    page: number;
    limit: number;
    items: Array<{
      __typename?: 'User';
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      phone?: string | null;
      dob?: any | null;
      dateJoined?: any | null;
      role?: UserRole | null;
      isDeleted?: boolean | null;
      canUseClubs?: boolean | null;
      association?: { __typename?: 'Association'; id: string; name: string } | null;
      contact?: { __typename?: 'Contact'; salesforceId: string; id: string } | null;
    }>;
  };
};

export type GetUserByIdQueryVariables = Exact<{
  userByIdId: Scalars['ID']['input'];
}>;

export type GetUserByIdQuery = {
  __typename?: 'Query';
  userById: {
    __typename?: 'User';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string | null;
    dob?: any | null;
    dateJoined?: any | null;
    role?: UserRole | null;
    isDeleted?: boolean | null;
    association?: { __typename?: 'Association'; id: string; name: string } | null;
  };
};

export type AssociationsQueryVariables = Exact<{
  paginationArgs?: InputMaybe<PaginationArgs>;
  search?: InputMaybe<Scalars['String']['input']>;
}>;

export type AssociationsQuery = {
  __typename?: 'Query';
  associations: {
    __typename?: 'PaginatedAssociationResponse';
    limit: number;
    page: number;
    total: number;
    items: Array<{ __typename?: 'Association'; id: string; name: string }>;
  };
};

export type AdminSendSignupEmailsMutationVariables = Exact<{
  input: SendSignupEmailsInput;
}>;

export type AdminSendSignupEmailsMutation = {
  __typename?: 'Mutation';
  adminSendSignupEmails: { __typename?: 'Message'; message: string };
};

export type AdminRemoveUserMutationVariables = Exact<{
  input: AdminRemoveUserInput;
}>;

export type AdminRemoveUserMutation = {
  __typename?: 'Mutation';
  adminRemoveUser: { __typename?: 'Message'; message: string };
};

export type ToggleUserClubFeatureMutationVariables = Exact<{
  userId: Scalars['ID']['input'];
}>;

export type ToggleUserClubFeatureMutation = {
  __typename?: 'Mutation';
  toggleUserClubFeature: {
    __typename?: 'User';
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string | null;
    dob?: any | null;
    birthdayMonth?: BirthdayMonth | null;
    birthdayDay?: number | null;
    dateJoined?: any | null;
    role?: UserRole | null;
    isDeleted?: boolean | null;
    canUseClubs?: boolean | null;
  };
};

export const AdminAssociationsDocument = gql`
  query AdminAssociations(
    $paginationArgs: PaginationArgs
    $filter: AdminAssociationsFilterInput
    $orderBy: AdminAssociationsOrderInput
  ) {
    adminAssociations(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
      items {
        activeClubCount
        canUseClubs
        clubMemberCount
        createdAt
        id
        memberCount
        name
        updatedAt
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useAdminAssociationsQuery__
 *
 * To run a query within a React component, call `useAdminAssociationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminAssociationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminAssociationsQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *      orderBy: // value for 'orderBy'
 *   },
 * });
 */
export function useAdminAssociationsQuery(
  baseOptions?: Apollo.QueryHookOptions<AdminAssociationsQuery, AdminAssociationsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminAssociationsQuery, AdminAssociationsQueryVariables>(
    AdminAssociationsDocument,
    options
  );
}
export function useAdminAssociationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminAssociationsQuery, AdminAssociationsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminAssociationsQuery, AdminAssociationsQueryVariables>(
    AdminAssociationsDocument,
    options
  );
}
export function useAdminAssociationsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminAssociationsQuery, AdminAssociationsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminAssociationsQuery, AdminAssociationsQueryVariables>(
    AdminAssociationsDocument,
    options
  );
}
export type AdminAssociationsQueryHookResult = ReturnType<typeof useAdminAssociationsQuery>;
export type AdminAssociationsLazyQueryHookResult = ReturnType<typeof useAdminAssociationsLazyQuery>;
export type AdminAssociationsSuspenseQueryHookResult = ReturnType<
  typeof useAdminAssociationsSuspenseQuery
>;
export type AdminAssociationsQueryResult = Apollo.QueryResult<
  AdminAssociationsQuery,
  AdminAssociationsQueryVariables
>;
export const AdminAssociationByIdDocument = gql`
  query AdminAssociationById($adminAssociationByIdId: ID!) {
    adminAssociationById(id: $adminAssociationByIdId) {
      clubMemberCount
      id
      name
      memberCount
      createdAt
      updatedAt
    }
  }
`;

/**
 * __useAdminAssociationByIdQuery__
 *
 * To run a query within a React component, call `useAdminAssociationByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminAssociationByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminAssociationByIdQuery({
 *   variables: {
 *      adminAssociationByIdId: // value for 'adminAssociationByIdId'
 *   },
 * });
 */
export function useAdminAssociationByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    AdminAssociationByIdQuery,
    AdminAssociationByIdQueryVariables
  > &
    ({ variables: AdminAssociationByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminAssociationByIdQuery, AdminAssociationByIdQueryVariables>(
    AdminAssociationByIdDocument,
    options
  );
}
export function useAdminAssociationByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AdminAssociationByIdQuery,
    AdminAssociationByIdQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminAssociationByIdQuery, AdminAssociationByIdQueryVariables>(
    AdminAssociationByIdDocument,
    options
  );
}
export function useAdminAssociationByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminAssociationByIdQuery, AdminAssociationByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminAssociationByIdQuery, AdminAssociationByIdQueryVariables>(
    AdminAssociationByIdDocument,
    options
  );
}
export type AdminAssociationByIdQueryHookResult = ReturnType<typeof useAdminAssociationByIdQuery>;
export type AdminAssociationByIdLazyQueryHookResult = ReturnType<
  typeof useAdminAssociationByIdLazyQuery
>;
export type AdminAssociationByIdSuspenseQueryHookResult = ReturnType<
  typeof useAdminAssociationByIdSuspenseQuery
>;
export type AdminAssociationByIdQueryResult = Apollo.QueryResult<
  AdminAssociationByIdQuery,
  AdminAssociationByIdQueryVariables
>;
export const ToggleAssociationClubFeatureDocument = gql`
  mutation toggleAssociationClubFeature($associationId: ID!) {
    toggleAssociationClubFeature(associationId: $associationId) {
      id
      name
      canUseClubs
    }
  }
`;
export type ToggleAssociationClubFeatureMutationFn = Apollo.MutationFunction<
  ToggleAssociationClubFeatureMutation,
  ToggleAssociationClubFeatureMutationVariables
>;

/**
 * __useToggleAssociationClubFeatureMutation__
 *
 * To run a mutation, you first call `useToggleAssociationClubFeatureMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useToggleAssociationClubFeatureMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [toggleAssociationClubFeatureMutation, { data, loading, error }] = useToggleAssociationClubFeatureMutation({
 *   variables: {
 *      associationId: // value for 'associationId'
 *   },
 * });
 */
export function useToggleAssociationClubFeatureMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ToggleAssociationClubFeatureMutation,
    ToggleAssociationClubFeatureMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ToggleAssociationClubFeatureMutation,
    ToggleAssociationClubFeatureMutationVariables
  >(ToggleAssociationClubFeatureDocument, options);
}
export type ToggleAssociationClubFeatureMutationHookResult = ReturnType<
  typeof useToggleAssociationClubFeatureMutation
>;
export type ToggleAssociationClubFeatureMutationResult =
  Apollo.MutationResult<ToggleAssociationClubFeatureMutation>;
export type ToggleAssociationClubFeatureMutationOptions = Apollo.BaseMutationOptions<
  ToggleAssociationClubFeatureMutation,
  ToggleAssociationClubFeatureMutationVariables
>;
export const MeDocument = gql`
  query me {
    me {
      id
      firstName
      lastName
      email
      role
    }
  }
`;

/**
 * __useMeQuery__
 *
 * To run a query within a React component, call `useMeQuery` and pass it any options that fit your needs.
 * When your component renders, `useMeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMeQuery({
 *   variables: {
 *   },
 * });
 */
export function useMeQuery(baseOptions?: Apollo.QueryHookOptions<MeQuery, MeQueryVariables>) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MeQuery, MeQueryVariables>(MeDocument, options);
}
export function useMeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<MeQuery, MeQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<MeQuery, MeQueryVariables>(MeDocument, options);
}
export function useMeSuspenseQuery(
  baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<MeQuery, MeQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<MeQuery, MeQueryVariables>(MeDocument, options);
}
export type MeQueryHookResult = ReturnType<typeof useMeQuery>;
export type MeLazyQueryHookResult = ReturnType<typeof useMeLazyQuery>;
export type MeSuspenseQueryHookResult = ReturnType<typeof useMeSuspenseQuery>;
export type MeQueryResult = Apollo.QueryResult<MeQuery, MeQueryVariables>;
export const RegisterDocument = gql`
  mutation register($input: RegisterInput!) {
    register(input: $input) {
      message
    }
  }
`;
export type RegisterMutationFn = Apollo.MutationFunction<
  RegisterMutation,
  RegisterMutationVariables
>;

/**
 * __useRegisterMutation__
 *
 * To run a mutation, you first call `useRegisterMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegisterMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registerMutation, { data, loading, error }] = useRegisterMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRegisterMutation(
  baseOptions?: Apollo.MutationHookOptions<RegisterMutation, RegisterMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<RegisterMutation, RegisterMutationVariables>(RegisterDocument, options);
}
export type RegisterMutationHookResult = ReturnType<typeof useRegisterMutation>;
export type RegisterMutationResult = Apollo.MutationResult<RegisterMutation>;
export type RegisterMutationOptions = Apollo.BaseMutationOptions<
  RegisterMutation,
  RegisterMutationVariables
>;
export const LoginDocument = gql`
  mutation login($input: LoginInput!) {
    login(input: $input) {
      accessToken
      refreshToken
      accessTokenExpireTime
      refreshTokenExpireTime
      role
    }
  }
`;
export type LoginMutationFn = Apollo.MutationFunction<LoginMutation, LoginMutationVariables>;

/**
 * __useLoginMutation__
 *
 * To run a mutation, you first call `useLoginMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLoginMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [loginMutation, { data, loading, error }] = useLoginMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useLoginMutation(
  baseOptions?: Apollo.MutationHookOptions<LoginMutation, LoginMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<LoginMutation, LoginMutationVariables>(LoginDocument, options);
}
export type LoginMutationHookResult = ReturnType<typeof useLoginMutation>;
export type LoginMutationResult = Apollo.MutationResult<LoginMutation>;
export type LoginMutationOptions = Apollo.BaseMutationOptions<
  LoginMutation,
  LoginMutationVariables
>;
export const RefreshTokenDocument = gql`
  mutation refreshToken($input: RefreshTokenInput!) {
    refreshToken(input: $input) {
      accessToken
      refreshToken
      accessTokenExpireTime
      refreshTokenExpireTime
    }
  }
`;
export type RefreshTokenMutationFn = Apollo.MutationFunction<
  RefreshTokenMutation,
  RefreshTokenMutationVariables
>;

/**
 * __useRefreshTokenMutation__
 *
 * To run a mutation, you first call `useRefreshTokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRefreshTokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [refreshTokenMutation, { data, loading, error }] = useRefreshTokenMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRefreshTokenMutation(
  baseOptions?: Apollo.MutationHookOptions<RefreshTokenMutation, RefreshTokenMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<RefreshTokenMutation, RefreshTokenMutationVariables>(
    RefreshTokenDocument,
    options
  );
}
export type RefreshTokenMutationHookResult = ReturnType<typeof useRefreshTokenMutation>;
export type RefreshTokenMutationResult = Apollo.MutationResult<RefreshTokenMutation>;
export type RefreshTokenMutationOptions = Apollo.BaseMutationOptions<
  RefreshTokenMutation,
  RefreshTokenMutationVariables
>;
export const ForgotPasswordDocument = gql`
  mutation forgotPassword($input: ForgotPasswordInput!) {
    forgotPassword(input: $input) {
      message
    }
  }
`;
export type ForgotPasswordMutationFn = Apollo.MutationFunction<
  ForgotPasswordMutation,
  ForgotPasswordMutationVariables
>;

/**
 * __useForgotPasswordMutation__
 *
 * To run a mutation, you first call `useForgotPasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useForgotPasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [forgotPasswordMutation, { data, loading, error }] = useForgotPasswordMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useForgotPasswordMutation(
  baseOptions?: Apollo.MutationHookOptions<ForgotPasswordMutation, ForgotPasswordMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ForgotPasswordMutation, ForgotPasswordMutationVariables>(
    ForgotPasswordDocument,
    options
  );
}
export type ForgotPasswordMutationHookResult = ReturnType<typeof useForgotPasswordMutation>;
export type ForgotPasswordMutationResult = Apollo.MutationResult<ForgotPasswordMutation>;
export type ForgotPasswordMutationOptions = Apollo.BaseMutationOptions<
  ForgotPasswordMutation,
  ForgotPasswordMutationVariables
>;
export const ResetPasswordDocument = gql`
  mutation resetPassword($input: ResetPasswordInput!) {
    resetPassword(input: $input) {
      message
    }
  }
`;
export type ResetPasswordMutationFn = Apollo.MutationFunction<
  ResetPasswordMutation,
  ResetPasswordMutationVariables
>;

/**
 * __useResetPasswordMutation__
 *
 * To run a mutation, you first call `useResetPasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useResetPasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [resetPasswordMutation, { data, loading, error }] = useResetPasswordMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useResetPasswordMutation(
  baseOptions?: Apollo.MutationHookOptions<ResetPasswordMutation, ResetPasswordMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ResetPasswordMutation, ResetPasswordMutationVariables>(
    ResetPasswordDocument,
    options
  );
}
export type ResetPasswordMutationHookResult = ReturnType<typeof useResetPasswordMutation>;
export type ResetPasswordMutationResult = Apollo.MutationResult<ResetPasswordMutation>;
export type ResetPasswordMutationOptions = Apollo.BaseMutationOptions<
  ResetPasswordMutation,
  ResetPasswordMutationVariables
>;
export const CompleteSignupDocument = gql`
  mutation CompleteSignup($input: CompleteSignupInput!) {
    completeSignup(input: $input) {
      accessToken
      refreshToken
      accessTokenExpireTime
      refreshTokenExpireTime
    }
  }
`;
export type CompleteSignupMutationFn = Apollo.MutationFunction<
  CompleteSignupMutation,
  CompleteSignupMutationVariables
>;

/**
 * __useCompleteSignupMutation__
 *
 * To run a mutation, you first call `useCompleteSignupMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCompleteSignupMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [completeSignupMutation, { data, loading, error }] = useCompleteSignupMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCompleteSignupMutation(
  baseOptions?: Apollo.MutationHookOptions<CompleteSignupMutation, CompleteSignupMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CompleteSignupMutation, CompleteSignupMutationVariables>(
    CompleteSignupDocument,
    options
  );
}
export type CompleteSignupMutationHookResult = ReturnType<typeof useCompleteSignupMutation>;
export type CompleteSignupMutationResult = Apollo.MutationResult<CompleteSignupMutation>;
export type CompleteSignupMutationOptions = Apollo.BaseMutationOptions<
  CompleteSignupMutation,
  CompleteSignupMutationVariables
>;
export const AdminClubDetailByIdDocument = gql`
  query AdminClubDetailById($adminClubByIdId: ID!) {
    adminClubById(id: $adminClubByIdId) {
      id
      activatedAt
      memberCount
      clubTemplate {
        id
        name
        description
        about
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
        category
        newPost
        hasJoined
        memberCount
      }
    }
  }
`;

/**
 * __useAdminClubDetailByIdQuery__
 *
 * To run a query within a React component, call `useAdminClubDetailByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubDetailByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubDetailByIdQuery({
 *   variables: {
 *      adminClubByIdId: // value for 'adminClubByIdId'
 *   },
 * });
 */
export function useAdminClubDetailByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    AdminClubDetailByIdQuery,
    AdminClubDetailByIdQueryVariables
  > &
    ({ variables: AdminClubDetailByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubDetailByIdQuery, AdminClubDetailByIdQueryVariables>(
    AdminClubDetailByIdDocument,
    options
  );
}
export function useAdminClubDetailByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AdminClubDetailByIdQuery,
    AdminClubDetailByIdQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubDetailByIdQuery, AdminClubDetailByIdQueryVariables>(
    AdminClubDetailByIdDocument,
    options
  );
}
export function useAdminClubDetailByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubDetailByIdQuery, AdminClubDetailByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubDetailByIdQuery, AdminClubDetailByIdQueryVariables>(
    AdminClubDetailByIdDocument,
    options
  );
}
export type AdminClubDetailByIdQueryHookResult = ReturnType<typeof useAdminClubDetailByIdQuery>;
export type AdminClubDetailByIdLazyQueryHookResult = ReturnType<
  typeof useAdminClubDetailByIdLazyQuery
>;
export type AdminClubDetailByIdSuspenseQueryHookResult = ReturnType<
  typeof useAdminClubDetailByIdSuspenseQuery
>;
export type AdminClubDetailByIdQueryResult = Apollo.QueryResult<
  AdminClubDetailByIdQuery,
  AdminClubDetailByIdQueryVariables
>;
export const ClubMembersDocument = gql`
  query ClubMembers(
    $clubTemplateId: ID!
    $paginationArgs: PaginationArgs
    $filter: ClubMembersFilterInput
    $orderBy: ClubMembersOrderInput
  ) {
    clubMembers(
      clubTemplateId: $clubTemplateId
      paginationArgs: $paginationArgs
      filter: $filter
      orderBy: $orderBy
    ) {
      items {
        id
        clubProfile {
          id
          displayName
          img {
            id
            filename
            key
            mimeType
            size
            status
            createdAt
            updatedAt
            url
          }
        }
        status
        joinedAt
        deletedAt
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useClubMembersQuery__
 *
 * To run a query within a React component, call `useClubMembersQuery` and pass it any options that fit your needs.
 * When your component renders, `useClubMembersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClubMembersQuery({
 *   variables: {
 *      clubTemplateId: // value for 'clubTemplateId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *      orderBy: // value for 'orderBy'
 *   },
 * });
 */
export function useClubMembersQuery(
  baseOptions: Apollo.QueryHookOptions<ClubMembersQuery, ClubMembersQueryVariables> &
    ({ variables: ClubMembersQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClubMembersQuery, ClubMembersQueryVariables>(ClubMembersDocument, options);
}
export function useClubMembersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ClubMembersQuery, ClubMembersQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClubMembersQuery, ClubMembersQueryVariables>(
    ClubMembersDocument,
    options
  );
}
export function useClubMembersSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ClubMembersQuery, ClubMembersQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ClubMembersQuery, ClubMembersQueryVariables>(
    ClubMembersDocument,
    options
  );
}
export type ClubMembersQueryHookResult = ReturnType<typeof useClubMembersQuery>;
export type ClubMembersLazyQueryHookResult = ReturnType<typeof useClubMembersLazyQuery>;
export type ClubMembersSuspenseQueryHookResult = ReturnType<typeof useClubMembersSuspenseQuery>;
export type ClubMembersQueryResult = Apollo.QueryResult<
  ClubMembersQuery,
  ClubMembersQueryVariables
>;
export const AdminClubPostsDocument = gql`
  query AdminClubPosts(
    $clubId: ID!
    $paginationArgs: PaginationArgs
    $filter: AdminClubPostsFilterInput
  ) {
    adminClubPosts(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
      items {
        id
        clubId
        clubProfile {
          id
          displayName
          img {
            id
            filename
            key
            mimeType
            size
            status
            createdAt
            updatedAt
            url
          }
          user {
            id
            firstName
            lastName
            email
            role
            canUseClubs
          }
          createdAt
          updatedAt
        }
        content
        isPinned
        reactions {
          id
          postId
          clubProfileId
          createdAt
        }
        reports {
          id
          clubPost {
            id
            clubId
            clubProfile {
              id
              displayName
            }
            content
            isPinned
            reactionCount
            createdAt
            updatedAt
          }
          category {
            id
            code
            title
            description
            ordering
            createdAt
            updatedAt
          }
          reporter {
            id
            displayName
            createdAt
            updatedAt
          }
          status
          details
          createdAt
        }
        reactionCount
        createdAt
        updatedAt
        deletedAt
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useAdminClubPostsQuery__
 *
 * To run a query within a React component, call `useAdminClubPostsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubPostsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubPostsQuery({
 *   variables: {
 *      clubId: // value for 'clubId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useAdminClubPostsQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubPostsQuery, AdminClubPostsQueryVariables> &
    ({ variables: AdminClubPostsQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubPostsQuery, AdminClubPostsQueryVariables>(
    AdminClubPostsDocument,
    options
  );
}
export function useAdminClubPostsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminClubPostsQuery, AdminClubPostsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubPostsQuery, AdminClubPostsQueryVariables>(
    AdminClubPostsDocument,
    options
  );
}
export function useAdminClubPostsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubPostsQuery, AdminClubPostsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubPostsQuery, AdminClubPostsQueryVariables>(
    AdminClubPostsDocument,
    options
  );
}
export type AdminClubPostsQueryHookResult = ReturnType<typeof useAdminClubPostsQuery>;
export type AdminClubPostsLazyQueryHookResult = ReturnType<typeof useAdminClubPostsLazyQuery>;
export type AdminClubPostsSuspenseQueryHookResult = ReturnType<
  typeof useAdminClubPostsSuspenseQuery
>;
export type AdminClubPostsQueryResult = Apollo.QueryResult<
  AdminClubPostsQuery,
  AdminClubPostsQueryVariables
>;
export const AdminClubPostByIdDocument = gql`
  query AdminClubPostById($postId: ID!) {
    adminClubPostById(postId: $postId) {
      id
      clubId
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
        user {
          id
          firstName
          lastName
          email
          addresses {
            id
            street
            city
            state
            zipCode
            isActive
            isPrimaryUnit
            source
            createdAt
            updatedAt
            isSameAsUnitAddress
          }
          phone
          dob
          birthdayMonth
          birthdayDay
          dateJoined
          association {
            id
            name
          }
          contact {
            id
            salesforceId
          }
          role
          isDeleted
        }
        createdAt
        updatedAt
      }
      content
      isPinned
      reactions {
        id
        postId
        clubProfileId
        createdAt
      }
      reports {
        id
        clubPost {
          id
          clubId
          clubProfile {
            id
            displayName
          }
          content
          isPinned
          reactionCount
          createdAt
          updatedAt
        }
        category {
          id
          code
          title
          description
          ordering
          createdAt
          updatedAt
        }
        reporter {
          id
          displayName
          createdAt
          updatedAt
        }
        status
        details
        createdAt
      }
      reactionCount
      createdAt
      updatedAt
      deletedAt
    }
  }
`;

/**
 * __useAdminClubPostByIdQuery__
 *
 * To run a query within a React component, call `useAdminClubPostByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubPostByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubPostByIdQuery({
 *   variables: {
 *      postId: // value for 'postId'
 *   },
 * });
 */
export function useAdminClubPostByIdQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubPostByIdQuery, AdminClubPostByIdQueryVariables> &
    ({ variables: AdminClubPostByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubPostByIdQuery, AdminClubPostByIdQueryVariables>(
    AdminClubPostByIdDocument,
    options
  );
}
export function useAdminClubPostByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminClubPostByIdQuery, AdminClubPostByIdQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubPostByIdQuery, AdminClubPostByIdQueryVariables>(
    AdminClubPostByIdDocument,
    options
  );
}
export function useAdminClubPostByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubPostByIdQuery, AdminClubPostByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubPostByIdQuery, AdminClubPostByIdQueryVariables>(
    AdminClubPostByIdDocument,
    options
  );
}
export type AdminClubPostByIdQueryHookResult = ReturnType<typeof useAdminClubPostByIdQuery>;
export type AdminClubPostByIdLazyQueryHookResult = ReturnType<typeof useAdminClubPostByIdLazyQuery>;
export type AdminClubPostByIdSuspenseQueryHookResult = ReturnType<
  typeof useAdminClubPostByIdSuspenseQuery
>;
export type AdminClubPostByIdQueryResult = Apollo.QueryResult<
  AdminClubPostByIdQuery,
  AdminClubPostByIdQueryVariables
>;
export const AdminClubEventsDocument = gql`
  query AdminClubEvents(
    $clubId: ID!
    $paginationArgs: PaginationArgs
    $filter: AdminClubEventsFilterInput
  ) {
    adminClubEvents(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
      items {
        id
        name
        startTime
        endTime
        location
        description
        reactionCount
        clubProfile {
          id
          displayName
          img {
            url
          }
          user {
            id
            firstName
            lastName
            email
            phone
            dob
            birthdayMonth
            birthdayDay
            dateJoined
            role
            isDeleted
            canUseClubs
          }
          createdAt
          updatedAt
        }
        reports {
          id
          category {
            id
            code
            title
            description
            ordering
            createdAt
            updatedAt
          }
          status
          details
          createdAt
        }
        createdAt
        updatedAt
        deletedAt
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useAdminClubEventsQuery__
 *
 * To run a query within a React component, call `useAdminClubEventsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubEventsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubEventsQuery({
 *   variables: {
 *      clubId: // value for 'clubId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useAdminClubEventsQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubEventsQuery, AdminClubEventsQueryVariables> &
    ({ variables: AdminClubEventsQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubEventsQuery, AdminClubEventsQueryVariables>(
    AdminClubEventsDocument,
    options
  );
}
export function useAdminClubEventsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminClubEventsQuery, AdminClubEventsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubEventsQuery, AdminClubEventsQueryVariables>(
    AdminClubEventsDocument,
    options
  );
}
export function useAdminClubEventsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubEventsQuery, AdminClubEventsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubEventsQuery, AdminClubEventsQueryVariables>(
    AdminClubEventsDocument,
    options
  );
}
export type AdminClubEventsQueryHookResult = ReturnType<typeof useAdminClubEventsQuery>;
export type AdminClubEventsLazyQueryHookResult = ReturnType<typeof useAdminClubEventsLazyQuery>;
export type AdminClubEventsSuspenseQueryHookResult = ReturnType<
  typeof useAdminClubEventsSuspenseQuery
>;
export type AdminClubEventsQueryResult = Apollo.QueryResult<
  AdminClubEventsQuery,
  AdminClubEventsQueryVariables
>;
export const UsersCreateEventsDocument = gql`
  query UsersCreateEvents(
    $clubId: ID!
    $paginationArgs: PaginationArgs
    $filter: UsersCreateEventsFilterInput
  ) {
    usersCreateEvents(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
      items {
        id
        firstName
        lastName
        email
        phone
        dob
        birthdayMonth
        birthdayDay
        dateJoined
        role
        isDeleted
        canUseClubs
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useUsersCreateEventsQuery__
 *
 * To run a query within a React component, call `useUsersCreateEventsQuery` and pass it any options that fit your needs.
 * When your component renders, `useUsersCreateEventsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUsersCreateEventsQuery({
 *   variables: {
 *      clubId: // value for 'clubId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useUsersCreateEventsQuery(
  baseOptions: Apollo.QueryHookOptions<UsersCreateEventsQuery, UsersCreateEventsQueryVariables> &
    ({ variables: UsersCreateEventsQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<UsersCreateEventsQuery, UsersCreateEventsQueryVariables>(
    UsersCreateEventsDocument,
    options
  );
}
export function useUsersCreateEventsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<UsersCreateEventsQuery, UsersCreateEventsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<UsersCreateEventsQuery, UsersCreateEventsQueryVariables>(
    UsersCreateEventsDocument,
    options
  );
}
export function useUsersCreateEventsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<UsersCreateEventsQuery, UsersCreateEventsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<UsersCreateEventsQuery, UsersCreateEventsQueryVariables>(
    UsersCreateEventsDocument,
    options
  );
}
export type UsersCreateEventsQueryHookResult = ReturnType<typeof useUsersCreateEventsQuery>;
export type UsersCreateEventsLazyQueryHookResult = ReturnType<typeof useUsersCreateEventsLazyQuery>;
export type UsersCreateEventsSuspenseQueryHookResult = ReturnType<
  typeof useUsersCreateEventsSuspenseQuery
>;
export type UsersCreateEventsQueryResult = Apollo.QueryResult<
  UsersCreateEventsQuery,
  UsersCreateEventsQueryVariables
>;
export const AdminClubEventByIdDocument = gql`
  query AdminClubEventById($clubEventId: ID!) {
    adminClubEventById(clubEventId: $clubEventId) {
      id
      name
      startTime
      endTime
      location
      description
      reactionCount
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
      }
      createdAt
      updatedAt
    }
  }
`;

/**
 * __useAdminClubEventByIdQuery__
 *
 * To run a query within a React component, call `useAdminClubEventByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubEventByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubEventByIdQuery({
 *   variables: {
 *      clubEventId: // value for 'clubEventId'
 *   },
 * });
 */
export function useAdminClubEventByIdQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubEventByIdQuery, AdminClubEventByIdQueryVariables> &
    ({ variables: AdminClubEventByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubEventByIdQuery, AdminClubEventByIdQueryVariables>(
    AdminClubEventByIdDocument,
    options
  );
}
export function useAdminClubEventByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AdminClubEventByIdQuery,
    AdminClubEventByIdQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubEventByIdQuery, AdminClubEventByIdQueryVariables>(
    AdminClubEventByIdDocument,
    options
  );
}
export function useAdminClubEventByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubEventByIdQuery, AdminClubEventByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubEventByIdQuery, AdminClubEventByIdQueryVariables>(
    AdminClubEventByIdDocument,
    options
  );
}
export type AdminClubEventByIdQueryHookResult = ReturnType<typeof useAdminClubEventByIdQuery>;
export type AdminClubEventByIdLazyQueryHookResult = ReturnType<
  typeof useAdminClubEventByIdLazyQuery
>;
export type AdminClubEventByIdSuspenseQueryHookResult = ReturnType<
  typeof useAdminClubEventByIdSuspenseQuery
>;
export type AdminClubEventByIdQueryResult = Apollo.QueryResult<
  AdminClubEventByIdQuery,
  AdminClubEventByIdQueryVariables
>;
export const ClubRequestByIdDocument = gql`
  query ClubRequestById($clubRequestByIdId: ID!) {
    clubRequestById(id: $clubRequestByIdId) {
      id
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
      }
      clubName
      clubDescription
      category
      clubAbout
      status
      createdAt
    }
  }
`;

/**
 * __useClubRequestByIdQuery__
 *
 * To run a query within a React component, call `useClubRequestByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useClubRequestByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClubRequestByIdQuery({
 *   variables: {
 *      clubRequestByIdId: // value for 'clubRequestByIdId'
 *   },
 * });
 */
export function useClubRequestByIdQuery(
  baseOptions: Apollo.QueryHookOptions<ClubRequestByIdQuery, ClubRequestByIdQueryVariables> &
    ({ variables: ClubRequestByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClubRequestByIdQuery, ClubRequestByIdQueryVariables>(
    ClubRequestByIdDocument,
    options
  );
}
export function useClubRequestByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ClubRequestByIdQuery, ClubRequestByIdQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClubRequestByIdQuery, ClubRequestByIdQueryVariables>(
    ClubRequestByIdDocument,
    options
  );
}
export function useClubRequestByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ClubRequestByIdQuery, ClubRequestByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ClubRequestByIdQuery, ClubRequestByIdQueryVariables>(
    ClubRequestByIdDocument,
    options
  );
}
export type ClubRequestByIdQueryHookResult = ReturnType<typeof useClubRequestByIdQuery>;
export type ClubRequestByIdLazyQueryHookResult = ReturnType<typeof useClubRequestByIdLazyQuery>;
export type ClubRequestByIdSuspenseQueryHookResult = ReturnType<
  typeof useClubRequestByIdSuspenseQuery
>;
export type ClubRequestByIdQueryResult = Apollo.QueryResult<
  ClubRequestByIdQuery,
  ClubRequestByIdQueryVariables
>;
export const AdminRemoveClubPostByIdDocument = gql`
  mutation AdminRemoveClubPostById($postId: ID!) {
    adminRemoveClubPostById(postId: $postId) {
      id
      clubId
      content
      isPinned
      reactionCount
      hasReacted
      createdAt
      updatedAt
    }
  }
`;
export type AdminRemoveClubPostByIdMutationFn = Apollo.MutationFunction<
  AdminRemoveClubPostByIdMutation,
  AdminRemoveClubPostByIdMutationVariables
>;

/**
 * __useAdminRemoveClubPostByIdMutation__
 *
 * To run a mutation, you first call `useAdminRemoveClubPostByIdMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminRemoveClubPostByIdMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminRemoveClubPostByIdMutation, { data, loading, error }] = useAdminRemoveClubPostByIdMutation({
 *   variables: {
 *      postId: // value for 'postId'
 *   },
 * });
 */
export function useAdminRemoveClubPostByIdMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminRemoveClubPostByIdMutation,
    AdminRemoveClubPostByIdMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AdminRemoveClubPostByIdMutation,
    AdminRemoveClubPostByIdMutationVariables
  >(AdminRemoveClubPostByIdDocument, options);
}
export type AdminRemoveClubPostByIdMutationHookResult = ReturnType<
  typeof useAdminRemoveClubPostByIdMutation
>;
export type AdminRemoveClubPostByIdMutationResult =
  Apollo.MutationResult<AdminRemoveClubPostByIdMutation>;
export type AdminRemoveClubPostByIdMutationOptions = Apollo.BaseMutationOptions<
  AdminRemoveClubPostByIdMutation,
  AdminRemoveClubPostByIdMutationVariables
>;
export const AdminClubMembersDocument = gql`
  query AdminClubMembers(
    $clubId: ID!
    $paginationArgs: PaginationArgs
    $filter: ClubMembersFilterInput
    $orderBy: ClubMembersOrderInput
  ) {
    adminClubMembers(
      clubId: $clubId
      paginationArgs: $paginationArgs
      filter: $filter
      orderBy: $orderBy
    ) {
      items {
        id
        clubProfile {
          id
          displayName
          img {
            id
            filename
            key
            mimeType
            size
            status
            createdAt
            updatedAt
            url
          }
          user {
            id
            firstName
            lastName
            email
            phone
            dob
            birthdayMonth
            birthdayDay
            dateJoined
            role
            isDeleted
            canUseClubs
          }
          createdAt
          updatedAt
        }
        status
        joinedAt
        deletedAt
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useAdminClubMembersQuery__
 *
 * To run a query within a React component, call `useAdminClubMembersQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubMembersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubMembersQuery({
 *   variables: {
 *      clubId: // value for 'clubId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *      orderBy: // value for 'orderBy'
 *   },
 * });
 */
export function useAdminClubMembersQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubMembersQuery, AdminClubMembersQueryVariables> &
    ({ variables: AdminClubMembersQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubMembersQuery, AdminClubMembersQueryVariables>(
    AdminClubMembersDocument,
    options
  );
}
export function useAdminClubMembersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminClubMembersQuery, AdminClubMembersQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubMembersQuery, AdminClubMembersQueryVariables>(
    AdminClubMembersDocument,
    options
  );
}
export function useAdminClubMembersSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubMembersQuery, AdminClubMembersQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubMembersQuery, AdminClubMembersQueryVariables>(
    AdminClubMembersDocument,
    options
  );
}
export type AdminClubMembersQueryHookResult = ReturnType<typeof useAdminClubMembersQuery>;
export type AdminClubMembersLazyQueryHookResult = ReturnType<typeof useAdminClubMembersLazyQuery>;
export type AdminClubMembersSuspenseQueryHookResult = ReturnType<
  typeof useAdminClubMembersSuspenseQuery
>;
export type AdminClubMembersQueryResult = Apollo.QueryResult<
  AdminClubMembersQuery,
  AdminClubMembersQueryVariables
>;
export const UsersCreatePostsDocument = gql`
  query UsersCreatePosts(
    $clubId: ID!
    $paginationArgs: PaginationArgs
    $filter: UsersCreatePostFilterInput
  ) {
    usersCreatePosts(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
      items {
        id
        firstName
        lastName
        email
        phone
        dob
        birthdayMonth
        birthdayDay
        dateJoined
        role
        isDeleted
        canUseClubs
      }
      limit
      page
      total
    }
  }
`;

/**
 * __useUsersCreatePostsQuery__
 *
 * To run a query within a React component, call `useUsersCreatePostsQuery` and pass it any options that fit your needs.
 * When your component renders, `useUsersCreatePostsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUsersCreatePostsQuery({
 *   variables: {
 *      clubId: // value for 'clubId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useUsersCreatePostsQuery(
  baseOptions: Apollo.QueryHookOptions<UsersCreatePostsQuery, UsersCreatePostsQueryVariables> &
    ({ variables: UsersCreatePostsQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<UsersCreatePostsQuery, UsersCreatePostsQueryVariables>(
    UsersCreatePostsDocument,
    options
  );
}
export function useUsersCreatePostsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<UsersCreatePostsQuery, UsersCreatePostsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<UsersCreatePostsQuery, UsersCreatePostsQueryVariables>(
    UsersCreatePostsDocument,
    options
  );
}
export function useUsersCreatePostsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<UsersCreatePostsQuery, UsersCreatePostsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<UsersCreatePostsQuery, UsersCreatePostsQueryVariables>(
    UsersCreatePostsDocument,
    options
  );
}
export type UsersCreatePostsQueryHookResult = ReturnType<typeof useUsersCreatePostsQuery>;
export type UsersCreatePostsLazyQueryHookResult = ReturnType<typeof useUsersCreatePostsLazyQuery>;
export type UsersCreatePostsSuspenseQueryHookResult = ReturnType<
  typeof useUsersCreatePostsSuspenseQuery
>;
export type UsersCreatePostsQueryResult = Apollo.QueryResult<
  UsersCreatePostsQuery,
  UsersCreatePostsQueryVariables
>;
export const AdminUnflagReportsByPostIdDocument = gql`
  mutation AdminUnflagReportsByPostId($postId: ID!) {
    adminUnflagReportsByPostId(postId: $postId) {
      clubId
      id
      updatedAt
      createdAt
    }
  }
`;
export type AdminUnflagReportsByPostIdMutationFn = Apollo.MutationFunction<
  AdminUnflagReportsByPostIdMutation,
  AdminUnflagReportsByPostIdMutationVariables
>;

/**
 * __useAdminUnflagReportsByPostIdMutation__
 *
 * To run a mutation, you first call `useAdminUnflagReportsByPostIdMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminUnflagReportsByPostIdMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminUnflagReportsByPostIdMutation, { data, loading, error }] = useAdminUnflagReportsByPostIdMutation({
 *   variables: {
 *      postId: // value for 'postId'
 *   },
 * });
 */
export function useAdminUnflagReportsByPostIdMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminUnflagReportsByPostIdMutation,
    AdminUnflagReportsByPostIdMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AdminUnflagReportsByPostIdMutation,
    AdminUnflagReportsByPostIdMutationVariables
  >(AdminUnflagReportsByPostIdDocument, options);
}
export type AdminUnflagReportsByPostIdMutationHookResult = ReturnType<
  typeof useAdminUnflagReportsByPostIdMutation
>;
export type AdminUnflagReportsByPostIdMutationResult =
  Apollo.MutationResult<AdminUnflagReportsByPostIdMutation>;
export type AdminUnflagReportsByPostIdMutationOptions = Apollo.BaseMutationOptions<
  AdminUnflagReportsByPostIdMutation,
  AdminUnflagReportsByPostIdMutationVariables
>;
export const AdminDeleteClubEventDocument = gql`
  mutation AdminDeleteClubEvent($clubEventId: ID!) {
    adminDeleteClubEvent(clubEventId: $clubEventId) {
      id
      name
      createdAt
      updatedAt
    }
  }
`;
export type AdminDeleteClubEventMutationFn = Apollo.MutationFunction<
  AdminDeleteClubEventMutation,
  AdminDeleteClubEventMutationVariables
>;

/**
 * __useAdminDeleteClubEventMutation__
 *
 * To run a mutation, you first call `useAdminDeleteClubEventMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminDeleteClubEventMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminDeleteClubEventMutation, { data, loading, error }] = useAdminDeleteClubEventMutation({
 *   variables: {
 *      clubEventId: // value for 'clubEventId'
 *   },
 * });
 */
export function useAdminDeleteClubEventMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminDeleteClubEventMutation,
    AdminDeleteClubEventMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AdminDeleteClubEventMutation, AdminDeleteClubEventMutationVariables>(
    AdminDeleteClubEventDocument,
    options
  );
}
export type AdminDeleteClubEventMutationHookResult = ReturnType<
  typeof useAdminDeleteClubEventMutation
>;
export type AdminDeleteClubEventMutationResult =
  Apollo.MutationResult<AdminDeleteClubEventMutation>;
export type AdminDeleteClubEventMutationOptions = Apollo.BaseMutationOptions<
  AdminDeleteClubEventMutation,
  AdminDeleteClubEventMutationVariables
>;
export const AdminUnflagReportsByEventIdDocument = gql`
  mutation AdminUnflagReportsByEventId($eventId: ID!) {
    adminUnflagReportsByEventId(eventId: $eventId) {
      id
      name
      createdAt
      updatedAt
    }
  }
`;
export type AdminUnflagReportsByEventIdMutationFn = Apollo.MutationFunction<
  AdminUnflagReportsByEventIdMutation,
  AdminUnflagReportsByEventIdMutationVariables
>;

/**
 * __useAdminUnflagReportsByEventIdMutation__
 *
 * To run a mutation, you first call `useAdminUnflagReportsByEventIdMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminUnflagReportsByEventIdMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminUnflagReportsByEventIdMutation, { data, loading, error }] = useAdminUnflagReportsByEventIdMutation({
 *   variables: {
 *      eventId: // value for 'eventId'
 *   },
 * });
 */
export function useAdminUnflagReportsByEventIdMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminUnflagReportsByEventIdMutation,
    AdminUnflagReportsByEventIdMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AdminUnflagReportsByEventIdMutation,
    AdminUnflagReportsByEventIdMutationVariables
  >(AdminUnflagReportsByEventIdDocument, options);
}
export type AdminUnflagReportsByEventIdMutationHookResult = ReturnType<
  typeof useAdminUnflagReportsByEventIdMutation
>;
export type AdminUnflagReportsByEventIdMutationResult =
  Apollo.MutationResult<AdminUnflagReportsByEventIdMutation>;
export type AdminUnflagReportsByEventIdMutationOptions = Apollo.BaseMutationOptions<
  AdminUnflagReportsByEventIdMutation,
  AdminUnflagReportsByEventIdMutationVariables
>;
export const RemoveClubMemberDocument = gql`
  mutation RemoveClubMember($input: RemoveClubMemberInput!) {
    removeClubMember(input: $input) {
      clubTemplateId
      membershipId
    }
  }
`;
export type RemoveClubMemberMutationFn = Apollo.MutationFunction<
  RemoveClubMemberMutation,
  RemoveClubMemberMutationVariables
>;

/**
 * __useRemoveClubMemberMutation__
 *
 * To run a mutation, you first call `useRemoveClubMemberMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveClubMemberMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeClubMemberMutation, { data, loading, error }] = useRemoveClubMemberMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRemoveClubMemberMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveClubMemberMutation,
    RemoveClubMemberMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<RemoveClubMemberMutation, RemoveClubMemberMutationVariables>(
    RemoveClubMemberDocument,
    options
  );
}
export type RemoveClubMemberMutationHookResult = ReturnType<typeof useRemoveClubMemberMutation>;
export type RemoveClubMemberMutationResult = Apollo.MutationResult<RemoveClubMemberMutation>;
export type RemoveClubMemberMutationOptions = Apollo.BaseMutationOptions<
  RemoveClubMemberMutation,
  RemoveClubMemberMutationVariables
>;
export const ClubTemplatesDocument = gql`
  query ClubTemplates(
    $paginationArgs: PaginationArgs
    $filter: ClubTemplatesFilterInput
    $orderBy: ClubOrderInput
  ) {
    clubTemplates(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
      items {
        id
        name
        description
        about
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
        category
        newPost
        hasJoined
        memberCount
        updatedAt
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useClubTemplatesQuery__
 *
 * To run a query within a React component, call `useClubTemplatesQuery` and pass it any options that fit your needs.
 * When your component renders, `useClubTemplatesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClubTemplatesQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *      orderBy: // value for 'orderBy'
 *   },
 * });
 */
export function useClubTemplatesQuery(
  baseOptions?: Apollo.QueryHookOptions<ClubTemplatesQuery, ClubTemplatesQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClubTemplatesQuery, ClubTemplatesQueryVariables>(
    ClubTemplatesDocument,
    options
  );
}
export function useClubTemplatesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ClubTemplatesQuery, ClubTemplatesQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClubTemplatesQuery, ClubTemplatesQueryVariables>(
    ClubTemplatesDocument,
    options
  );
}
export function useClubTemplatesSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ClubTemplatesQuery, ClubTemplatesQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ClubTemplatesQuery, ClubTemplatesQueryVariables>(
    ClubTemplatesDocument,
    options
  );
}
export type ClubTemplatesQueryHookResult = ReturnType<typeof useClubTemplatesQuery>;
export type ClubTemplatesLazyQueryHookResult = ReturnType<typeof useClubTemplatesLazyQuery>;
export type ClubTemplatesSuspenseQueryHookResult = ReturnType<typeof useClubTemplatesSuspenseQuery>;
export type ClubTemplatesQueryResult = Apollo.QueryResult<
  ClubTemplatesQuery,
  ClubTemplatesQueryVariables
>;
export const ClubTemplateByIdDocument = gql`
  query ClubTemplateById($clubTemplateByIdId: ID!) {
    clubTemplateById(id: $clubTemplateByIdId) {
      id
      name
      description
      about
      category
      newPost
      img {
        id
        filename
        url
      }
    }
  }
`;

/**
 * __useClubTemplateByIdQuery__
 *
 * To run a query within a React component, call `useClubTemplateByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useClubTemplateByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClubTemplateByIdQuery({
 *   variables: {
 *      clubTemplateByIdId: // value for 'clubTemplateByIdId'
 *   },
 * });
 */
export function useClubTemplateByIdQuery(
  baseOptions: Apollo.QueryHookOptions<ClubTemplateByIdQuery, ClubTemplateByIdQueryVariables> &
    ({ variables: ClubTemplateByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClubTemplateByIdQuery, ClubTemplateByIdQueryVariables>(
    ClubTemplateByIdDocument,
    options
  );
}
export function useClubTemplateByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ClubTemplateByIdQuery, ClubTemplateByIdQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClubTemplateByIdQuery, ClubTemplateByIdQueryVariables>(
    ClubTemplateByIdDocument,
    options
  );
}
export function useClubTemplateByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ClubTemplateByIdQuery, ClubTemplateByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ClubTemplateByIdQuery, ClubTemplateByIdQueryVariables>(
    ClubTemplateByIdDocument,
    options
  );
}
export type ClubTemplateByIdQueryHookResult = ReturnType<typeof useClubTemplateByIdQuery>;
export type ClubTemplateByIdLazyQueryHookResult = ReturnType<typeof useClubTemplateByIdLazyQuery>;
export type ClubTemplateByIdSuspenseQueryHookResult = ReturnType<
  typeof useClubTemplateByIdSuspenseQuery
>;
export type ClubTemplateByIdQueryResult = Apollo.QueryResult<
  ClubTemplateByIdQuery,
  ClubTemplateByIdQueryVariables
>;
export const AdminClubByIdDocument = gql`
  query AdminClubById($adminClubByIdId: ID!) {
    adminClubById(id: $adminClubByIdId) {
      id
      activatedAt
      memberCount
      clubTemplate {
        id
        name
        description
        about
        category
        newPost
        img {
          id
          filename
          url
        }
      }
    }
  }
`;

/**
 * __useAdminClubByIdQuery__
 *
 * To run a query within a React component, call `useAdminClubByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubByIdQuery({
 *   variables: {
 *      adminClubByIdId: // value for 'adminClubByIdId'
 *   },
 * });
 */
export function useAdminClubByIdQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubByIdQuery, AdminClubByIdQueryVariables> &
    ({ variables: AdminClubByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubByIdQuery, AdminClubByIdQueryVariables>(
    AdminClubByIdDocument,
    options
  );
}
export function useAdminClubByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminClubByIdQuery, AdminClubByIdQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubByIdQuery, AdminClubByIdQueryVariables>(
    AdminClubByIdDocument,
    options
  );
}
export function useAdminClubByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubByIdQuery, AdminClubByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubByIdQuery, AdminClubByIdQueryVariables>(
    AdminClubByIdDocument,
    options
  );
}
export type AdminClubByIdQueryHookResult = ReturnType<typeof useAdminClubByIdQuery>;
export type AdminClubByIdLazyQueryHookResult = ReturnType<typeof useAdminClubByIdLazyQuery>;
export type AdminClubByIdSuspenseQueryHookResult = ReturnType<typeof useAdminClubByIdSuspenseQuery>;
export type AdminClubByIdQueryResult = Apollo.QueryResult<
  AdminClubByIdQuery,
  AdminClubByIdQueryVariables
>;
export const AdminClubsDocument = gql`
  query AdminClubs(
    $associationId: ID!
    $paginationArgs: PaginationArgs
    $filter: ClubFilterInput
    $orderBy: ClubOrderInput
  ) {
    adminClubs(
      associationId: $associationId
      paginationArgs: $paginationArgs
      filter: $filter
      orderBy: $orderBy
    ) {
      items {
        id
        activatedAt
        lastActivity
        memberCount
        clubTemplate {
          id
          name
          description
          about
          category
          newPost
          img {
            id
            filename
            url
          }
        }
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useAdminClubsQuery__
 *
 * To run a query within a React component, call `useAdminClubsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminClubsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminClubsQuery({
 *   variables: {
 *      associationId: // value for 'associationId'
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *      orderBy: // value for 'orderBy'
 *   },
 * });
 */
export function useAdminClubsQuery(
  baseOptions: Apollo.QueryHookOptions<AdminClubsQuery, AdminClubsQueryVariables> &
    ({ variables: AdminClubsQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminClubsQuery, AdminClubsQueryVariables>(AdminClubsDocument, options);
}
export function useAdminClubsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AdminClubsQuery, AdminClubsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminClubsQuery, AdminClubsQueryVariables>(
    AdminClubsDocument,
    options
  );
}
export function useAdminClubsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminClubsQuery, AdminClubsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminClubsQuery, AdminClubsQueryVariables>(
    AdminClubsDocument,
    options
  );
}
export type AdminClubsQueryHookResult = ReturnType<typeof useAdminClubsQuery>;
export type AdminClubsLazyQueryHookResult = ReturnType<typeof useAdminClubsLazyQuery>;
export type AdminClubsSuspenseQueryHookResult = ReturnType<typeof useAdminClubsSuspenseQuery>;
export type AdminClubsQueryResult = Apollo.QueryResult<AdminClubsQuery, AdminClubsQueryVariables>;
export const CreateClubTemplateDocument = gql`
  mutation CreateClubTemplate($input: CreateClubTemplateInput!) {
    createClubTemplate(input: $input) {
      id
      name
      description
      about
      category
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      newPost
      hasJoined
      memberCount
    }
  }
`;
export type CreateClubTemplateMutationFn = Apollo.MutationFunction<
  CreateClubTemplateMutation,
  CreateClubTemplateMutationVariables
>;

/**
 * __useCreateClubTemplateMutation__
 *
 * To run a mutation, you first call `useCreateClubTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateClubTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createClubTemplateMutation, { data, loading, error }] = useCreateClubTemplateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateClubTemplateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateClubTemplateMutation,
    CreateClubTemplateMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateClubTemplateMutation, CreateClubTemplateMutationVariables>(
    CreateClubTemplateDocument,
    options
  );
}
export type CreateClubTemplateMutationHookResult = ReturnType<typeof useCreateClubTemplateMutation>;
export type CreateClubTemplateMutationResult = Apollo.MutationResult<CreateClubTemplateMutation>;
export type CreateClubTemplateMutationOptions = Apollo.BaseMutationOptions<
  CreateClubTemplateMutation,
  CreateClubTemplateMutationVariables
>;
export const UpdateClubTemplateDocument = gql`
  mutation UpdateClubTemplate($input: UpdateClubTemplateInput!) {
    updateClubTemplate(input: $input) {
      id
      name
      description
      about
      category
      img {
        id
        filename
        url
      }
      newPost
    }
  }
`;
export type UpdateClubTemplateMutationFn = Apollo.MutationFunction<
  UpdateClubTemplateMutation,
  UpdateClubTemplateMutationVariables
>;

/**
 * __useUpdateClubTemplateMutation__
 *
 * To run a mutation, you first call `useUpdateClubTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateClubTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateClubTemplateMutation, { data, loading, error }] = useUpdateClubTemplateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateClubTemplateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateClubTemplateMutation,
    UpdateClubTemplateMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateClubTemplateMutation, UpdateClubTemplateMutationVariables>(
    UpdateClubTemplateDocument,
    options
  );
}
export type UpdateClubTemplateMutationHookResult = ReturnType<typeof useUpdateClubTemplateMutation>;
export type UpdateClubTemplateMutationResult = Apollo.MutationResult<UpdateClubTemplateMutation>;
export type UpdateClubTemplateMutationOptions = Apollo.BaseMutationOptions<
  UpdateClubTemplateMutation,
  UpdateClubTemplateMutationVariables
>;
export const DeleteClubTemplateDocument = gql`
  mutation DeleteClubTemplate($deleteClubTemplateId: ID!) {
    deleteClubTemplate(id: $deleteClubTemplateId)
  }
`;
export type DeleteClubTemplateMutationFn = Apollo.MutationFunction<
  DeleteClubTemplateMutation,
  DeleteClubTemplateMutationVariables
>;

/**
 * __useDeleteClubTemplateMutation__
 *
 * To run a mutation, you first call `useDeleteClubTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteClubTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteClubTemplateMutation, { data, loading, error }] = useDeleteClubTemplateMutation({
 *   variables: {
 *      deleteClubTemplateId: // value for 'deleteClubTemplateId'
 *   },
 * });
 */
export function useDeleteClubTemplateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteClubTemplateMutation,
    DeleteClubTemplateMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<DeleteClubTemplateMutation, DeleteClubTemplateMutationVariables>(
    DeleteClubTemplateDocument,
    options
  );
}
export type DeleteClubTemplateMutationHookResult = ReturnType<typeof useDeleteClubTemplateMutation>;
export type DeleteClubTemplateMutationResult = Apollo.MutationResult<DeleteClubTemplateMutation>;
export type DeleteClubTemplateMutationOptions = Apollo.BaseMutationOptions<
  DeleteClubTemplateMutation,
  DeleteClubTemplateMutationVariables
>;
export const CreateUploadUrlDocument = gql`
  mutation CreateUploadUrl($input: CreateUploadFileInput!) {
    createUploadUrl(input: $input) {
      uploadFile {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      presignedUrl {
        url
        fields {
          bucket
          key
          Policy
          X_Amz_Algorithm
          X_Amz_Credential
          X_Amz_Date
          X_Amz_Signature
        }
      }
    }
  }
`;
export type CreateUploadUrlMutationFn = Apollo.MutationFunction<
  CreateUploadUrlMutation,
  CreateUploadUrlMutationVariables
>;

/**
 * __useCreateUploadUrlMutation__
 *
 * To run a mutation, you first call `useCreateUploadUrlMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateUploadUrlMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createUploadUrlMutation, { data, loading, error }] = useCreateUploadUrlMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateUploadUrlMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateUploadUrlMutation,
    CreateUploadUrlMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateUploadUrlMutation, CreateUploadUrlMutationVariables>(
    CreateUploadUrlDocument,
    options
  );
}
export type CreateUploadUrlMutationHookResult = ReturnType<typeof useCreateUploadUrlMutation>;
export type CreateUploadUrlMutationResult = Apollo.MutationResult<CreateUploadUrlMutation>;
export type CreateUploadUrlMutationOptions = Apollo.BaseMutationOptions<
  CreateUploadUrlMutation,
  CreateUploadUrlMutationVariables
>;
export const ClubRequestsDocument = gql`
  query ClubRequests(
    $paginationArgs: PaginationArgs
    $filter: ClubRequestFilterInput
    $orderBy: ClubRequestOrderInput
  ) {
    clubRequests(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
      items {
        category
        clubAbout
        clubDescription
        clubName
        clubProfile {
          id
          displayName
          img {
            id
            filename
            key
            mimeType
            size
            status
            createdAt
            updatedAt
            url
          }
        }
        createdAt
        id
        status
        user {
          id
          firstName
          lastName
          email
          phone
          dob
          birthdayMonth
          birthdayDay
          dateJoined
          role
          isDeleted
          canUseClubs
        }
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useClubRequestsQuery__
 *
 * To run a query within a React component, call `useClubRequestsQuery` and pass it any options that fit your needs.
 * When your component renders, `useClubRequestsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useClubRequestsQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *      orderBy: // value for 'orderBy'
 *   },
 * });
 */
export function useClubRequestsQuery(
  baseOptions?: Apollo.QueryHookOptions<ClubRequestsQuery, ClubRequestsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ClubRequestsQuery, ClubRequestsQueryVariables>(
    ClubRequestsDocument,
    options
  );
}
export function useClubRequestsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ClubRequestsQuery, ClubRequestsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ClubRequestsQuery, ClubRequestsQueryVariables>(
    ClubRequestsDocument,
    options
  );
}
export function useClubRequestsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ClubRequestsQuery, ClubRequestsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ClubRequestsQuery, ClubRequestsQueryVariables>(
    ClubRequestsDocument,
    options
  );
}
export type ClubRequestsQueryHookResult = ReturnType<typeof useClubRequestsQuery>;
export type ClubRequestsLazyQueryHookResult = ReturnType<typeof useClubRequestsLazyQuery>;
export type ClubRequestsSuspenseQueryHookResult = ReturnType<typeof useClubRequestsSuspenseQuery>;
export type ClubRequestsQueryResult = Apollo.QueryResult<
  ClubRequestsQuery,
  ClubRequestsQueryVariables
>;
export const UpdateClubRequestDocument = gql`
  mutation UpdateClubRequest($input: UpdateClubRequestInput!) {
    updateClubRequest(input: $input) {
      id
      clubProfile {
        id
        displayName
      }
      clubName
      clubDescription
      category
      clubAbout
      status
      createdAt
    }
  }
`;
export type UpdateClubRequestMutationFn = Apollo.MutationFunction<
  UpdateClubRequestMutation,
  UpdateClubRequestMutationVariables
>;

/**
 * __useUpdateClubRequestMutation__
 *
 * To run a mutation, you first call `useUpdateClubRequestMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateClubRequestMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateClubRequestMutation, { data, loading, error }] = useUpdateClubRequestMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateClubRequestMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateClubRequestMutation,
    UpdateClubRequestMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateClubRequestMutation, UpdateClubRequestMutationVariables>(
    UpdateClubRequestDocument,
    options
  );
}
export type UpdateClubRequestMutationHookResult = ReturnType<typeof useUpdateClubRequestMutation>;
export type UpdateClubRequestMutationResult = Apollo.MutationResult<UpdateClubRequestMutation>;
export type UpdateClubRequestMutationOptions = Apollo.BaseMutationOptions<
  UpdateClubRequestMutation,
  UpdateClubRequestMutationVariables
>;
export const UsersRequestedClubCreationDocument = gql`
  query UsersRequestedClubCreation(
    $paginationArgs: PaginationArgs
    $filter: UsersRequestedClubCreationFilterInput
  ) {
    usersRequestedClubCreation(paginationArgs: $paginationArgs, filter: $filter) {
      items {
        id
        firstName
        lastName
        email
        phone
        dob
        birthdayMonth
        birthdayDay
        dateJoined
        role
        isDeleted
        canUseClubs
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useUsersRequestedClubCreationQuery__
 *
 * To run a query within a React component, call `useUsersRequestedClubCreationQuery` and pass it any options that fit your needs.
 * When your component renders, `useUsersRequestedClubCreationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUsersRequestedClubCreationQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useUsersRequestedClubCreationQuery(
  baseOptions?: Apollo.QueryHookOptions<
    UsersRequestedClubCreationQuery,
    UsersRequestedClubCreationQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<UsersRequestedClubCreationQuery, UsersRequestedClubCreationQueryVariables>(
    UsersRequestedClubCreationDocument,
    options
  );
}
export function useUsersRequestedClubCreationLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    UsersRequestedClubCreationQuery,
    UsersRequestedClubCreationQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    UsersRequestedClubCreationQuery,
    UsersRequestedClubCreationQueryVariables
  >(UsersRequestedClubCreationDocument, options);
}
export function useUsersRequestedClubCreationSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        UsersRequestedClubCreationQuery,
        UsersRequestedClubCreationQueryVariables
      >
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    UsersRequestedClubCreationQuery,
    UsersRequestedClubCreationQueryVariables
  >(UsersRequestedClubCreationDocument, options);
}
export type UsersRequestedClubCreationQueryHookResult = ReturnType<
  typeof useUsersRequestedClubCreationQuery
>;
export type UsersRequestedClubCreationLazyQueryHookResult = ReturnType<
  typeof useUsersRequestedClubCreationLazyQuery
>;
export type UsersRequestedClubCreationSuspenseQueryHookResult = ReturnType<
  typeof useUsersRequestedClubCreationSuspenseQuery
>;
export type UsersRequestedClubCreationQueryResult = Apollo.QueryResult<
  UsersRequestedClubCreationQuery,
  UsersRequestedClubCreationQueryVariables
>;
export const ApprovedClubRequestCreationDocument = gql`
  mutation ApprovedClubRequestCreation($clubRequestId: ID!, $clubTemplateId: ID!) {
    approvedClubRequestCreation(clubRequestId: $clubRequestId, clubTemplateId: $clubTemplateId) {
      id
      clubName
      clubDescription
      category
      clubAbout
      status
      createdAt
    }
  }
`;
export type ApprovedClubRequestCreationMutationFn = Apollo.MutationFunction<
  ApprovedClubRequestCreationMutation,
  ApprovedClubRequestCreationMutationVariables
>;

/**
 * __useApprovedClubRequestCreationMutation__
 *
 * To run a mutation, you first call `useApprovedClubRequestCreationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useApprovedClubRequestCreationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [approvedClubRequestCreationMutation, { data, loading, error }] = useApprovedClubRequestCreationMutation({
 *   variables: {
 *      clubRequestId: // value for 'clubRequestId'
 *      clubTemplateId: // value for 'clubTemplateId'
 *   },
 * });
 */
export function useApprovedClubRequestCreationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ApprovedClubRequestCreationMutation,
    ApprovedClubRequestCreationMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ApprovedClubRequestCreationMutation,
    ApprovedClubRequestCreationMutationVariables
  >(ApprovedClubRequestCreationDocument, options);
}
export type ApprovedClubRequestCreationMutationHookResult = ReturnType<
  typeof useApprovedClubRequestCreationMutation
>;
export type ApprovedClubRequestCreationMutationResult =
  Apollo.MutationResult<ApprovedClubRequestCreationMutation>;
export type ApprovedClubRequestCreationMutationOptions = Apollo.BaseMutationOptions<
  ApprovedClubRequestCreationMutation,
  ApprovedClubRequestCreationMutationVariables
>;
export const RejectedClubRequestCreationDocument = gql`
  mutation RejectedClubRequestCreation($clubRequestId: ID!) {
    rejectedClubRequestCreation(clubRequestId: $clubRequestId) {
      id
      clubName
      clubDescription
      category
      clubAbout
      status
      createdAt
    }
  }
`;
export type RejectedClubRequestCreationMutationFn = Apollo.MutationFunction<
  RejectedClubRequestCreationMutation,
  RejectedClubRequestCreationMutationVariables
>;

/**
 * __useRejectedClubRequestCreationMutation__
 *
 * To run a mutation, you first call `useRejectedClubRequestCreationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRejectedClubRequestCreationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [rejectedClubRequestCreationMutation, { data, loading, error }] = useRejectedClubRequestCreationMutation({
 *   variables: {
 *      clubRequestId: // value for 'clubRequestId'
 *   },
 * });
 */
export function useRejectedClubRequestCreationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RejectedClubRequestCreationMutation,
    RejectedClubRequestCreationMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RejectedClubRequestCreationMutation,
    RejectedClubRequestCreationMutationVariables
  >(RejectedClubRequestCreationDocument, options);
}
export type RejectedClubRequestCreationMutationHookResult = ReturnType<
  typeof useRejectedClubRequestCreationMutation
>;
export type RejectedClubRequestCreationMutationResult =
  Apollo.MutationResult<RejectedClubRequestCreationMutation>;
export type RejectedClubRequestCreationMutationOptions = Apollo.BaseMutationOptions<
  RejectedClubRequestCreationMutation,
  RejectedClubRequestCreationMutationVariables
>;
export const AssociationsByUserIdDocument = gql`
  query associationsByUserId($input: AssociationsByUserIdInput!) {
    associationsByUserId(input: $input) {
      id
      name
    }
  }
`;

/**
 * __useAssociationsByUserIdQuery__
 *
 * To run a query within a React component, call `useAssociationsByUserIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAssociationsByUserIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAssociationsByUserIdQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAssociationsByUserIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    AssociationsByUserIdQuery,
    AssociationsByUserIdQueryVariables
  > &
    ({ variables: AssociationsByUserIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AssociationsByUserIdQuery, AssociationsByUserIdQueryVariables>(
    AssociationsByUserIdDocument,
    options
  );
}
export function useAssociationsByUserIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AssociationsByUserIdQuery,
    AssociationsByUserIdQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AssociationsByUserIdQuery, AssociationsByUserIdQueryVariables>(
    AssociationsByUserIdDocument,
    options
  );
}
export function useAssociationsByUserIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AssociationsByUserIdQuery, AssociationsByUserIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AssociationsByUserIdQuery, AssociationsByUserIdQueryVariables>(
    AssociationsByUserIdDocument,
    options
  );
}
export type AssociationsByUserIdQueryHookResult = ReturnType<typeof useAssociationsByUserIdQuery>;
export type AssociationsByUserIdLazyQueryHookResult = ReturnType<
  typeof useAssociationsByUserIdLazyQuery
>;
export type AssociationsByUserIdSuspenseQueryHookResult = ReturnType<
  typeof useAssociationsByUserIdSuspenseQuery
>;
export type AssociationsByUserIdQueryResult = Apollo.QueryResult<
  AssociationsByUserIdQuery,
  AssociationsByUserIdQueryVariables
>;
export const AdminUpdateProfileDocument = gql`
  mutation adminUpdateProfile($input: AdminUpdateProfileInput!) {
    adminUpdateProfile(input: $input) {
      id
      firstName
      lastName
      email
      association {
        id
        name
      }
      role
    }
  }
`;
export type AdminUpdateProfileMutationFn = Apollo.MutationFunction<
  AdminUpdateProfileMutation,
  AdminUpdateProfileMutationVariables
>;

/**
 * __useAdminUpdateProfileMutation__
 *
 * To run a mutation, you first call `useAdminUpdateProfileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminUpdateProfileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminUpdateProfileMutation, { data, loading, error }] = useAdminUpdateProfileMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAdminUpdateProfileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminUpdateProfileMutation,
    AdminUpdateProfileMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AdminUpdateProfileMutation, AdminUpdateProfileMutationVariables>(
    AdminUpdateProfileDocument,
    options
  );
}
export type AdminUpdateProfileMutationHookResult = ReturnType<typeof useAdminUpdateProfileMutation>;
export type AdminUpdateProfileMutationResult = Apollo.MutationResult<AdminUpdateProfileMutation>;
export type AdminUpdateProfileMutationOptions = Apollo.BaseMutationOptions<
  AdminUpdateProfileMutation,
  AdminUpdateProfileMutationVariables
>;
export const AdminNotificationsDocument = gql`
  query AdminNotifications($paginationArgs: PaginationArgs) {
    adminNotifications(paginationArgs: $paginationArgs) {
      items {
        id
        type
        isRead
        createdAt
        updatedAt
        reportedAt
        payload {
          postId
          eventId
        }
        clubPost {
          id
          clubId
          clubProfile {
            id
            displayName
            img {
              id
              filename
              key
              mimeType
              size
              status
              createdAt
              updatedAt
              url
            }
            user {
              id
              firstName
              lastName
              email
              addresses {
                id
                street
                city
                state
                zipCode
                isActive
                isPrimaryUnit
                source
                createdAt
                updatedAt
                isSameAsUnitAddress
              }
              phone
              dob
              birthdayMonth
              birthdayDay
              dateJoined
              association {
                id
                name
                canUseClubs
              }
              contact {
                id
                salesforceId
              }
              role
              isDeleted
              canUseClubs
            }
            createdAt
            updatedAt
          }
          content
          isPinned
          reactions {
            id
            postId
            clubProfileId
            createdAt
          }
          reports {
            id
            clubPost {
              id
              clubId
              clubProfile {
                id
                displayName
              }
              content
              isPinned
              isNew
              reactionCount
              hasReacted
              createdAt
              updatedAt
            }
            clubEvent {
              id
              name
              startTime
              endTime
              location
              description
              reactionCount
              isNew
              hasReacted
              createdAt
              updatedAt
            }
            category {
              id
              code
              title
              description
              ordering
              createdAt
              updatedAt
            }
            reporter {
              id
              displayName
              createdAt
              updatedAt
            }
            status
            details
            createdAt
          }
          reactionCount
          createdAt
          updatedAt
          deletedAt
        }
        clubEvent {
          clubId
          id
          name
          startTime
          endTime
          location
          description
          reactionCount
          createdAt
          updatedAt
          clubProfile {
            id
            displayName
            img {
              url
            }
            user {
              association {
                id
                name
                canUseClubs
              }
            }
            createdAt
            updatedAt
          }
          reports {
            id
            category {
              id
              code
              title
              description
            }
            status
            details
            createdAt
          }
        }
        clubRequest {
          id
          clubProfile {
            id
            displayName
            img {
              url
            }
          }
          user {
            firstName
            id
            lastName
          }
          clubName
          clubDescription
          category
          clubAbout
          status
          createdAt
        }
        action
      }
      limit
      page
      total
    }
  }
`;

/**
 * __useAdminNotificationsQuery__
 *
 * To run a query within a React component, call `useAdminNotificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAdminNotificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAdminNotificationsQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *   },
 * });
 */
export function useAdminNotificationsQuery(
  baseOptions?: Apollo.QueryHookOptions<AdminNotificationsQuery, AdminNotificationsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AdminNotificationsQuery, AdminNotificationsQueryVariables>(
    AdminNotificationsDocument,
    options
  );
}
export function useAdminNotificationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AdminNotificationsQuery,
    AdminNotificationsQueryVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AdminNotificationsQuery, AdminNotificationsQueryVariables>(
    AdminNotificationsDocument,
    options
  );
}
export function useAdminNotificationsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AdminNotificationsQuery, AdminNotificationsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AdminNotificationsQuery, AdminNotificationsQueryVariables>(
    AdminNotificationsDocument,
    options
  );
}
export type AdminNotificationsQueryHookResult = ReturnType<typeof useAdminNotificationsQuery>;
export type AdminNotificationsLazyQueryHookResult = ReturnType<
  typeof useAdminNotificationsLazyQuery
>;
export type AdminNotificationsSuspenseQueryHookResult = ReturnType<
  typeof useAdminNotificationsSuspenseQuery
>;
export type AdminNotificationsQueryResult = Apollo.QueryResult<
  AdminNotificationsQuery,
  AdminNotificationsQueryVariables
>;
export const MarkAdminNotificationsAsReadDocument = gql`
  mutation MarkAdminNotificationsAsRead($input: MarkNotificationsAsReadInput!) {
    markAdminNotificationsAsRead(input: $input)
  }
`;
export type MarkAdminNotificationsAsReadMutationFn = Apollo.MutationFunction<
  MarkAdminNotificationsAsReadMutation,
  MarkAdminNotificationsAsReadMutationVariables
>;

/**
 * __useMarkAdminNotificationsAsReadMutation__
 *
 * To run a mutation, you first call `useMarkAdminNotificationsAsReadMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMarkAdminNotificationsAsReadMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [markAdminNotificationsAsReadMutation, { data, loading, error }] = useMarkAdminNotificationsAsReadMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMarkAdminNotificationsAsReadMutation(
  baseOptions?: Apollo.MutationHookOptions<
    MarkAdminNotificationsAsReadMutation,
    MarkAdminNotificationsAsReadMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    MarkAdminNotificationsAsReadMutation,
    MarkAdminNotificationsAsReadMutationVariables
  >(MarkAdminNotificationsAsReadDocument, options);
}
export type MarkAdminNotificationsAsReadMutationHookResult = ReturnType<
  typeof useMarkAdminNotificationsAsReadMutation
>;
export type MarkAdminNotificationsAsReadMutationResult =
  Apollo.MutationResult<MarkAdminNotificationsAsReadMutation>;
export type MarkAdminNotificationsAsReadMutationOptions = Apollo.BaseMutationOptions<
  MarkAdminNotificationsAsReadMutation,
  MarkAdminNotificationsAsReadMutationVariables
>;
export const ListStaffDocument = gql`
  query listStaff {
    listStaff {
      id
      firstName
      lastName
      email
      addresses {
        id
        street
        city
        state
        zipCode
        isActive
        source
        createdAt
        updatedAt
      }
      phone
      dob
      dateJoined
      association {
        id
        name
      }
      role
    }
  }
`;

/**
 * __useListStaffQuery__
 *
 * To run a query within a React component, call `useListStaffQuery` and pass it any options that fit your needs.
 * When your component renders, `useListStaffQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListStaffQuery({
 *   variables: {
 *   },
 * });
 */
export function useListStaffQuery(
  baseOptions?: Apollo.QueryHookOptions<ListStaffQuery, ListStaffQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<ListStaffQuery, ListStaffQueryVariables>(ListStaffDocument, options);
}
export function useListStaffLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<ListStaffQuery, ListStaffQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<ListStaffQuery, ListStaffQueryVariables>(ListStaffDocument, options);
}
export function useListStaffSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<ListStaffQuery, ListStaffQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<ListStaffQuery, ListStaffQueryVariables>(
    ListStaffDocument,
    options
  );
}
export type ListStaffQueryHookResult = ReturnType<typeof useListStaffQuery>;
export type ListStaffLazyQueryHookResult = ReturnType<typeof useListStaffLazyQuery>;
export type ListStaffSuspenseQueryHookResult = ReturnType<typeof useListStaffSuspenseQuery>;
export type ListStaffQueryResult = Apollo.QueryResult<ListStaffQuery, ListStaffQueryVariables>;
export const UpdateProfileDocument = gql`
  mutation UpdateProfile($input: UpdateProfileInput!) {
    updateProfile(input: $input) {
      id
      firstName
      lastName
      email
    }
  }
`;
export type UpdateProfileMutationFn = Apollo.MutationFunction<
  UpdateProfileMutation,
  UpdateProfileMutationVariables
>;

/**
 * __useUpdateProfileMutation__
 *
 * To run a mutation, you first call `useUpdateProfileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateProfileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateProfileMutation, { data, loading, error }] = useUpdateProfileMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateProfileMutation(
  baseOptions?: Apollo.MutationHookOptions<UpdateProfileMutation, UpdateProfileMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateProfileMutation, UpdateProfileMutationVariables>(
    UpdateProfileDocument,
    options
  );
}
export type UpdateProfileMutationHookResult = ReturnType<typeof useUpdateProfileMutation>;
export type UpdateProfileMutationResult = Apollo.MutationResult<UpdateProfileMutation>;
export type UpdateProfileMutationOptions = Apollo.BaseMutationOptions<
  UpdateProfileMutation,
  UpdateProfileMutationVariables
>;
export const UpdatePasswordDocument = gql`
  mutation UpdatePassword($input: UpdatePasswordInput!) {
    updatePassword(input: $input) {
      message
    }
  }
`;
export type UpdatePasswordMutationFn = Apollo.MutationFunction<
  UpdatePasswordMutation,
  UpdatePasswordMutationVariables
>;

/**
 * __useUpdatePasswordMutation__
 *
 * To run a mutation, you first call `useUpdatePasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePasswordMutation, { data, loading, error }] = useUpdatePasswordMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePasswordMutation(
  baseOptions?: Apollo.MutationHookOptions<UpdatePasswordMutation, UpdatePasswordMutationVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdatePasswordMutation, UpdatePasswordMutationVariables>(
    UpdatePasswordDocument,
    options
  );
}
export type UpdatePasswordMutationHookResult = ReturnType<typeof useUpdatePasswordMutation>;
export type UpdatePasswordMutationResult = Apollo.MutationResult<UpdatePasswordMutation>;
export type UpdatePasswordMutationOptions = Apollo.BaseMutationOptions<
  UpdatePasswordMutation,
  UpdatePasswordMutationVariables
>;
export const UnsubscribeEmailDocument = gql`
  mutation unsubscribeEmail($input: UnsubscribeInput!) {
    unsubscribeEmail(input: $input) {
      message
    }
  }
`;
export type UnsubscribeEmailMutationFn = Apollo.MutationFunction<
  UnsubscribeEmailMutation,
  UnsubscribeEmailMutationVariables
>;

/**
 * __useUnsubscribeEmailMutation__
 *
 * To run a mutation, you first call `useUnsubscribeEmailMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUnsubscribeEmailMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [unsubscribeEmailMutation, { data, loading, error }] = useUnsubscribeEmailMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUnsubscribeEmailMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UnsubscribeEmailMutation,
    UnsubscribeEmailMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UnsubscribeEmailMutation, UnsubscribeEmailMutationVariables>(
    UnsubscribeEmailDocument,
    options
  );
}
export type UnsubscribeEmailMutationHookResult = ReturnType<typeof useUnsubscribeEmailMutation>;
export type UnsubscribeEmailMutationResult = Apollo.MutationResult<UnsubscribeEmailMutation>;
export type UnsubscribeEmailMutationOptions = Apollo.BaseMutationOptions<
  UnsubscribeEmailMutation,
  UnsubscribeEmailMutationVariables
>;
export const UsersDocument = gql`
  query users(
    $paginationArgs: PaginationArgs!
    $sort: UserSortInput
    $filter: UserFilterInput
    $search: String
  ) {
    users(paginationArgs: $paginationArgs, sort: $sort, filter: $filter, search: $search) {
      items {
        id
        firstName
        lastName
        email
        phone
        dob
        association {
          id
          name
        }
        dateJoined
        contact {
          salesforceId
          id
        }
        role
        isDeleted
        canUseClubs
      }
      total
      page
      limit
    }
  }
`;

/**
 * __useUsersQuery__
 *
 * To run a query within a React component, call `useUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUsersQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *      search: // value for 'search'
 *   },
 * });
 */
export function useUsersQuery(
  baseOptions: Apollo.QueryHookOptions<UsersQuery, UsersQueryVariables> &
    ({ variables: UsersQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<UsersQuery, UsersQueryVariables>(UsersDocument, options);
}
export function useUsersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<UsersQuery, UsersQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<UsersQuery, UsersQueryVariables>(UsersDocument, options);
}
export function useUsersSuspenseQuery(
  baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<UsersQuery, UsersQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<UsersQuery, UsersQueryVariables>(UsersDocument, options);
}
export type UsersQueryHookResult = ReturnType<typeof useUsersQuery>;
export type UsersLazyQueryHookResult = ReturnType<typeof useUsersLazyQuery>;
export type UsersSuspenseQueryHookResult = ReturnType<typeof useUsersSuspenseQuery>;
export type UsersQueryResult = Apollo.QueryResult<UsersQuery, UsersQueryVariables>;
export const GetUserByIdDocument = gql`
  query getUserById($userByIdId: ID!) {
    userById(id: $userByIdId) {
      id
      firstName
      lastName
      email
      phone
      dob
      dateJoined
      association {
        id
        name
      }
      role
      isDeleted
    }
  }
`;

/**
 * __useGetUserByIdQuery__
 *
 * To run a query within a React component, call `useGetUserByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserByIdQuery({
 *   variables: {
 *      userByIdId: // value for 'userByIdId'
 *   },
 * });
 */
export function useGetUserByIdQuery(
  baseOptions: Apollo.QueryHookOptions<GetUserByIdQuery, GetUserByIdQueryVariables> &
    ({ variables: GetUserByIdQueryVariables; skip?: boolean } | { skip: boolean })
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetUserByIdQuery, GetUserByIdQueryVariables>(GetUserByIdDocument, options);
}
export function useGetUserByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<GetUserByIdQuery, GetUserByIdQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetUserByIdQuery, GetUserByIdQueryVariables>(
    GetUserByIdDocument,
    options
  );
}
export function useGetUserByIdSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<GetUserByIdQuery, GetUserByIdQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetUserByIdQuery, GetUserByIdQueryVariables>(
    GetUserByIdDocument,
    options
  );
}
export type GetUserByIdQueryHookResult = ReturnType<typeof useGetUserByIdQuery>;
export type GetUserByIdLazyQueryHookResult = ReturnType<typeof useGetUserByIdLazyQuery>;
export type GetUserByIdSuspenseQueryHookResult = ReturnType<typeof useGetUserByIdSuspenseQuery>;
export type GetUserByIdQueryResult = Apollo.QueryResult<
  GetUserByIdQuery,
  GetUserByIdQueryVariables
>;
export const AssociationsDocument = gql`
  query associations($paginationArgs: PaginationArgs, $search: String) {
    associations(paginationArgs: $paginationArgs, search: $search) {
      items {
        id
        name
      }
      limit
      page
      total
    }
  }
`;

/**
 * __useAssociationsQuery__
 *
 * To run a query within a React component, call `useAssociationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAssociationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAssociationsQuery({
 *   variables: {
 *      paginationArgs: // value for 'paginationArgs'
 *      search: // value for 'search'
 *   },
 * });
 */
export function useAssociationsQuery(
  baseOptions?: Apollo.QueryHookOptions<AssociationsQuery, AssociationsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<AssociationsQuery, AssociationsQueryVariables>(
    AssociationsDocument,
    options
  );
}
export function useAssociationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<AssociationsQuery, AssociationsQueryVariables>
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<AssociationsQuery, AssociationsQueryVariables>(
    AssociationsDocument,
    options
  );
}
export function useAssociationsSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<AssociationsQuery, AssociationsQueryVariables>
) {
  const options =
    baseOptions === Apollo.skipToken ? baseOptions : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<AssociationsQuery, AssociationsQueryVariables>(
    AssociationsDocument,
    options
  );
}
export type AssociationsQueryHookResult = ReturnType<typeof useAssociationsQuery>;
export type AssociationsLazyQueryHookResult = ReturnType<typeof useAssociationsLazyQuery>;
export type AssociationsSuspenseQueryHookResult = ReturnType<typeof useAssociationsSuspenseQuery>;
export type AssociationsQueryResult = Apollo.QueryResult<
  AssociationsQuery,
  AssociationsQueryVariables
>;
export const AdminSendSignupEmailsDocument = gql`
  mutation adminSendSignupEmails($input: SendSignupEmailsInput!) {
    adminSendSignupEmails(input: $input) {
      message
    }
  }
`;
export type AdminSendSignupEmailsMutationFn = Apollo.MutationFunction<
  AdminSendSignupEmailsMutation,
  AdminSendSignupEmailsMutationVariables
>;

/**
 * __useAdminSendSignupEmailsMutation__
 *
 * To run a mutation, you first call `useAdminSendSignupEmailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminSendSignupEmailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminSendSignupEmailsMutation, { data, loading, error }] = useAdminSendSignupEmailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAdminSendSignupEmailsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminSendSignupEmailsMutation,
    AdminSendSignupEmailsMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AdminSendSignupEmailsMutation, AdminSendSignupEmailsMutationVariables>(
    AdminSendSignupEmailsDocument,
    options
  );
}
export type AdminSendSignupEmailsMutationHookResult = ReturnType<
  typeof useAdminSendSignupEmailsMutation
>;
export type AdminSendSignupEmailsMutationResult =
  Apollo.MutationResult<AdminSendSignupEmailsMutation>;
export type AdminSendSignupEmailsMutationOptions = Apollo.BaseMutationOptions<
  AdminSendSignupEmailsMutation,
  AdminSendSignupEmailsMutationVariables
>;
export const AdminRemoveUserDocument = gql`
  mutation adminRemoveUser($input: AdminRemoveUserInput!) {
    adminRemoveUser(input: $input) {
      message
    }
  }
`;
export type AdminRemoveUserMutationFn = Apollo.MutationFunction<
  AdminRemoveUserMutation,
  AdminRemoveUserMutationVariables
>;

/**
 * __useAdminRemoveUserMutation__
 *
 * To run a mutation, you first call `useAdminRemoveUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAdminRemoveUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [adminRemoveUserMutation, { data, loading, error }] = useAdminRemoveUserMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAdminRemoveUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AdminRemoveUserMutation,
    AdminRemoveUserMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AdminRemoveUserMutation, AdminRemoveUserMutationVariables>(
    AdminRemoveUserDocument,
    options
  );
}
export type AdminRemoveUserMutationHookResult = ReturnType<typeof useAdminRemoveUserMutation>;
export type AdminRemoveUserMutationResult = Apollo.MutationResult<AdminRemoveUserMutation>;
export type AdminRemoveUserMutationOptions = Apollo.BaseMutationOptions<
  AdminRemoveUserMutation,
  AdminRemoveUserMutationVariables
>;
export const ToggleUserClubFeatureDocument = gql`
  mutation toggleUserClubFeature($userId: ID!) {
    toggleUserClubFeature(userId: $userId) {
      id
      firstName
      lastName
      email
      phone
      dob
      birthdayMonth
      birthdayDay
      dateJoined
      role
      isDeleted
      canUseClubs
    }
  }
`;
export type ToggleUserClubFeatureMutationFn = Apollo.MutationFunction<
  ToggleUserClubFeatureMutation,
  ToggleUserClubFeatureMutationVariables
>;

/**
 * __useToggleUserClubFeatureMutation__
 *
 * To run a mutation, you first call `useToggleUserClubFeatureMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useToggleUserClubFeatureMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [toggleUserClubFeatureMutation, { data, loading, error }] = useToggleUserClubFeatureMutation({
 *   variables: {
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useToggleUserClubFeatureMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ToggleUserClubFeatureMutation,
    ToggleUserClubFeatureMutationVariables
  >
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ToggleUserClubFeatureMutation, ToggleUserClubFeatureMutationVariables>(
    ToggleUserClubFeatureDocument,
    options
  );
}
export type ToggleUserClubFeatureMutationHookResult = ReturnType<
  typeof useToggleUserClubFeatureMutation
>;
export type ToggleUserClubFeatureMutationResult =
  Apollo.MutationResult<ToggleUserClubFeatureMutation>;
export type ToggleUserClubFeatureMutationOptions = Apollo.BaseMutationOptions<
  ToggleUserClubFeatureMutation,
  ToggleUserClubFeatureMutationVariables
>;
