import { useMemo, useCallback, useRef } from 'react';
import TableData from '@/components/ui/table/TableData';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { AdminClubMembership, UserRole } from '@/generated/graphql';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import TableHeaderFilters from './TableHeaderFilters';
import { Row } from '@tanstack/react-table';
import { generateMemberColumns } from './MemberColumns';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { useMemberFilters } from './hooks/useMemberFilters';
import { useMemberActions } from './hooks/useMemberActions';
import { DisableClubAccessModal } from './DisableClubAccessModal';

interface MembersTabProps {
  clubId: string;
}

const MembersTab = ({ clubId }: MembersTabProps) => {
  const { userData, isLoading: isLoadingAuth } = useAuthContext();
  const actionCellRef = useRef<HTMLDivElement>(null);

  // Use custom hooks for filtering and actions
  const {
    selectedRows,
    setSelectedRows,
    selectedRowId,
    setSelectedRowId,
    columnFilters,
    setColumnFilters,
    dateJoined,
    setDateJoined,
    pagination,
    setPagination,
    sorting,
    setSorting,
    searchTemp,
    setSearchTemp,
    members,
    totalMembers,
    isLoadingMembers,
  } = useMemberFilters(clubId);

  const {
    isDisableModalOpen,
    isDisablingClubAccess,
    setIsDisableModalOpen,
    handleOpenDisableModal,
    handleCloseDisableModal,
    handleDisableClubAccessMember,
  } = useMemberActions();

  const columns = useMemo(
    () =>
      generateMemberColumns({
        isLoading: isLoadingMembers || isLoadingAuth,
        showActions: userData?.role === UserRole.Admin,
        selectedRowId,
        actionCellRef,
        onDisableClubAccess: handleOpenDisableModal,
        setSelectedRowId,
      }),
    [
      handleOpenDisableModal,
      isLoadingAuth,
      isLoadingMembers,
      selectedRowId,
      setSelectedRowId,
      userData?.role,
    ]
  );

  const handleRowClick = useCallback(
    (row: Row<AdminClubMembership>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        actionCellRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start',
        });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedRowId]
  );

  return (
    <div className='w-full flex flex-col py-6  bg-white rounded-lg'>
      <TableHeaderFilters
        search={searchTemp}
        setSearch={setSearchTemp}
        dateJoined={dateJoined}
        setDateJoined={setDateJoined}
      />
      <div className='w-full border rounded-lg overflow-auto'>
        <TableHeaderCount title='Members' total={totalMembers} />
        <TableData
          columns={columns}
          data={members}
          pagination={pagination}
          sorting={sorting}
          filters={columnFilters}
          onColumnFiltersChange={setColumnFilters}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        {totalMembers > 0 && (
          <TablePagination
            pageCount={Math.ceil(totalMembers / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        )}
      </div>

      {/* Disable Club Access Modal */}
      <DisableClubAccessModal
        isOpen={isDisableModalOpen}
        isDisabling={isDisablingClubAccess}
        onOpenChange={setIsDisableModalOpen}
        onCancel={handleCloseDisableModal}
        onConfirm={handleDisableClubAccessMember}
      />
    </div>
  );
};

export default MembersTab;
