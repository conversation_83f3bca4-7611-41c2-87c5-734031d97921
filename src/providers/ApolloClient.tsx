import { Apollo<PERSON><PERSON><PERSON> } from '@apollo/client';
import {
  ApolloClient,
  ApolloLink,
  fromPromise,
  HttpLink,
  InMemoryCache,
  NormalizedCacheObject,
} from '@apollo/client/core';
import { setContext } from '@apollo/client/link/context';
import { ErrorResponse, onError } from '@apollo/client/link/error';
import React, { useMemo } from 'react';
import removeEmptyKeys from '@/lib/RemoveEmptyKeys';
import { refreshTokenVar } from '@/lib/cache';

interface Props {
  children: React.ReactNode;
}

type ReturnType = {
  client: ApolloClient<NormalizedCacheObject>;
};

const ApolloClientContext = React.createContext<ReturnType | undefined>(undefined);

export const useApolloClient = () => React.useContext(ApolloClientContext);

const getHeaders = async (
  headers: Record<string, string> = {}
): Promise<Record<string, string>> => {
  const authData = localStorage.getItem('auth');
  const { accessToken } = authData ? JSON.parse(authData) : { accessToken: undefined };

  return removeEmptyKeys({
    ...headers,
    Authorization: accessToken ? `Bearer ${accessToken}` : '',
  });
};

const ApolloClientProvider = ({ children }: Props) => {
  const client = useMemo(() => {
    const authLink = setContext(async (_, { headers }) => {
      const newHeaders = await getHeaders(headers);
      return {
        headers: newHeaders,
      };
    });

    const cache = new InMemoryCache({
      typePolicies: {
        Query: {
          fields: {
            associations: {
              keyArgs: ['search'],
              merge(existing: any, incoming: any) {
                if (!existing) return incoming;

                const existingItems = existing.items || [];
                const incomingItems = incoming.items || [];

                return {
                  ...incoming,
                  items: [...existingItems, ...incomingItems],
                };
              },
            },
            adminNotifications: {
              keyArgs: false,
              merge(existing: any, incoming: any, { args, readField }: any) {
                if (!existing) return incoming;

                const existingItems = existing.items || [];
                const incomingItems = incoming.items || [];

                // // If this is a refetch (page 1), merge intelligently
                // if (args?.paginationArgs?.page === 1) {
                //   // Create a map of existing items by ID for efficient lookup
                //   const existingMap = new Map(
                //     existingItems.map((item: any) => [readField('id', item), item])
                //   );

                //   // Update existing items with new data, preserving order
                //   const mergedItems = existingItems.map((existingItem: any) => {
                //     const existingId = readField('id', existingItem);
                //     const incomingItem = incomingItems.find(
                //       (item: any) => readField('id', item) === existingId
                //     );

                //     // If the item exists in incoming data, use the updated version
                //     // If not, check if it still exists on the server (not in first page means deleted)
                //     return incomingItem || existingItem;
                //   });

                //   // Add any new items from incoming that don't exist in our cache
                //   const newItems = incomingItems.filter((incomingItem: any) => {
                //     const incomingId = readField('id', incomingItem);
                //     return !existingMap.has(incomingId);
                //   });

                //   // Combine: new items first, then existing items (updated)
                //   const allItems = [
                //     ...newItems,
                //     ...mergedItems.filter((item: any) => {
                //       const itemId = readField('id', item);
                //       // Keep items that are either in the incoming data or beyond the first page
                //       return (
                //         incomingItems.some((inc: any) => readField('id', inc) === itemId) ||
                //         existingItems.indexOf(item) >= incomingItems.length
                //       );
                //     }),
                //   ];

                //   return {
                //     ...incoming,
                //     items: allItems,
                //     total: incoming.total,
                //   };
                // }

                // For pagination (loading more), append items
                return {
                  ...incoming,
                  items: [...existingItems, ...incomingItems],
                  total: incoming.total,
                };
              },
            },
          },
        },
      },
    });

    const errorLink: any = onError(
      ({ graphQLErrors, networkError, response: _response, operation, forward }: ErrorResponse) => {
        if (
          (networkError as any)?.statusCode === 401 ||
          (graphQLErrors?.[0]?.extensions?.code === 'UNAUTHORIZED' &&
            graphQLErrors?.[0]?.message === 'Jwt expired')
        ) {
          // Don't try to refresh if the operation was a refresh token request
          if (operation.operationName === 'RefreshToken') {
            return;
          }

          // If the error is a 401, we need to refresh the token and try again.
          const headers = operation.getContext().headers;
          return fromPromise(
            refreshTokenVar()!().then((result) => {
              if (!result) {
                // If refresh failed, don't retry the operation
                return false;
              }
              return getHeaders(headers).then((newHeaders: any) => {
                operation.setContext({
                  headers: newHeaders,
                });
                return true;
              });
            })
          )
            .filter((value) => !!value)
            .flatMap(() => forward(operation));
        }
      }
    );

    const client = new ApolloClient({
      defaultOptions: {
        watchQuery: {
          errorPolicy: 'all',
        },
      },
      link: ApolloLink.from([
        authLink,
        errorLink,
        new HttpLink({
          uri: (() => {
            return process.env.REACT_APP_API_URL;
          })(),
        }),
      ]),
      cache,
    });

    return {
      client,
    };
  }, []);

  return (
    <ApolloClientContext.Provider value={client}>
      <ApolloProvider client={client?.client}>{children}</ApolloProvider>
    </ApolloClientContext.Provider>
  );
};

export { ApolloClientContext, ApolloClientProvider };
